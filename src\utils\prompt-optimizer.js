/**
 * 提示词优化器 (Node.js版本)
 * 用于优化和增强图片生成的提示词
 */

import { Logger } from './logger.js';

export class PromptOptimizer {
  constructor() {
    this.logger = new Logger();
    
    // 质量提升词汇
    this.qualityEnhancers = [
      'high quality', 'detailed', 'professional', '8k resolution',
      'masterpiece', 'best quality', 'ultra detailed', 'sharp focus'
    ];

    // 风格增强词汇
    this.styleEnhancers = {
      'realistic': ['photorealistic', 'hyperrealistic', 'lifelike', 'natural lighting'],
      'cartoon': ['vibrant colors', 'clean lines', 'stylized', 'animated style'],
      'minimalist': ['clean', 'simple', 'geometric', 'negative space'],
      'artistic': ['painterly', 'expressive', 'creative', 'artistic composition'],
      'professional': ['corporate', 'business style', 'polished', 'commercial']
    };

    // 技术增强词汇
    this.technicalEnhancers = [
      'perfect composition', 'rule of thirds', 'balanced lighting',
      'depth of field', 'cinematic', 'studio lighting'
    ];

    // 负面提示词
    this.negativePrompts = [
      'low quality', 'blurry', 'distorted', 'watermark', 'text',
      'signature', 'username', 'error', 'cropped', 'worst quality',
      'low resolution', 'jpeg artifacts', 'duplicate', 'morbid'
    ];

    // 颜色相关词汇
    this.colorKeywords = {
      'warm': ['warm colors', 'golden hour', 'sunset lighting', 'amber tones'],
      'cool': ['cool colors', 'blue tones', 'moonlight', 'winter palette'],
      'vibrant': ['vibrant colors', 'saturated', 'colorful', 'bright'],
      'muted': ['muted colors', 'pastel', 'soft tones', 'desaturated']
    };
  }

  /**
   * 优化提示词
   * @param {Object} options - 优化选项
   * @returns {Object} 优化结果
   */
  async optimizePrompt(options = {}) {
    const {
      prompt,
      style = 'realistic',
      enhance_quality = true,
      add_technical_terms = true,
      color_preference = null
    } = options;

    this.logger.info(`🔧 开始优化提示词: ${prompt.substring(0, 50)}...`);

    try {
      const improvements = [];
      let optimizedParts = [prompt.trim()];

      // 清理原始提示词
      const cleanedPrompt = this.cleanPrompt(prompt);
      if (cleanedPrompt !== prompt) {
        improvements.push('清理了重复和无效词汇');
        optimizedParts = [cleanedPrompt];
      }

      // 添加风格增强
      if (this.styleEnhancers[style]) {
        const styleWords = this.styleEnhancers[style].slice(0, 2); // 限制数量
        optimizedParts.push(...styleWords);
        improvements.push(`添加了${style}风格增强词`);
      }

      // 添加颜色偏好
      if (color_preference && this.colorKeywords[color_preference]) {
        const colorWords = this.colorKeywords[color_preference].slice(0, 1);
        optimizedParts.push(...colorWords);
        improvements.push(`添加了${color_preference}色调`);
      }

      // 添加技术术语
      if (add_technical_terms) {
        const techWords = this.technicalEnhancers.slice(0, 2);
        optimizedParts.push(...techWords);
        improvements.push('添加了技术增强词');
      }

      // 添加质量增强
      if (enhance_quality) {
        const qualityWords = this.qualityEnhancers.slice(0, 3);
        optimizedParts.push(...qualityWords);
        improvements.push('添加了质量增强词');
      }

      // 组合优化后的提示词
      const optimized = optimizedParts.join(', ');

      // 计算质量分数
      const qualityScore = this.calculateQualityScore(optimized);

      const result = {
        success: true,
        original: prompt,
        optimized: optimized,
        improvements: improvements,
        quality_score: qualityScore
      };

      this.logger.info(`✅ 提示词优化完成，质量分数: ${qualityScore.toFixed(2)}`);
      return result;

    } catch (error) {
      this.logger.error('❌ 提示词优化失败:', error);
      return {
        success: false,
        error: error.message,
        original: prompt
      };
    }
  }

  /**
   * 清理提示词
   */
  cleanPrompt(prompt) {
    // 移除多余的空格和标点
    let cleaned = prompt.replace(/\s+/g, ' ').trim();
    cleaned = cleaned.replace(/[,\s]+,/g, ',');
    cleaned = cleaned.replace(/^,|,$/g, '');

    // 分割并去重
    const parts = cleaned.split(',').map(part => part.trim());
    const uniqueParts = [];
    const seen = new Set();

    for (const part of parts) {
      const partLower = part.toLowerCase();
      if (!seen.has(partLower) && part) {
        uniqueParts.push(part);
        seen.add(partLower);
      }
    }

    return uniqueParts.join(', ');
  }

  /**
   * 计算提示词质量分数
   */
  calculateQualityScore(prompt) {
    let score = 0.0;
    const promptLower = prompt.toLowerCase();

    // 基础分数
    score += 0.3;

    // 长度分数（适中的长度更好）
    const wordCount = prompt.split(',').length;
    if (wordCount >= 5 && wordCount <= 15) {
      score += 0.2;
    } else if (wordCount > 15) {
      score += 0.1;
    }

    // 质量词汇分数
    const qualityCount = this.qualityEnhancers.reduce((count, word) => {
      return count + (promptLower.includes(word) ? 1 : 0);
    }, 0);
    score += Math.min(qualityCount * 0.1, 0.3);

    // 技术词汇分数
    const techCount = this.technicalEnhancers.reduce((count, word) => {
      return count + (promptLower.includes(word) ? 1 : 0);
    }, 0);
    score += Math.min(techCount * 0.05, 0.2);

    // 负面词汇扣分
    const negativeCount = this.negativePrompts.reduce((count, word) => {
      return count + (promptLower.includes(word) ? 1 : 0);
    }, 0);
    score -= negativeCount * 0.1;

    return Math.max(0.0, Math.min(1.0, score));
  }

  /**
   * 生成负面提示词
   */
  generateNegativePrompt(style = 'realistic') {
    const baseNegative = this.negativePrompts.slice(0, 8);

    // 根据风格添加特定的负面词汇
    const styleSpecific = {
      'realistic': ['cartoon', 'anime', 'painting', 'sketch'],
      'cartoon': ['photorealistic', 'realistic', 'photography'],
      'minimalist': ['cluttered', 'busy', 'complex', 'ornate'],
      'artistic': ['boring', 'plain', 'simple', 'mundane']
    };

    if (styleSpecific[style]) {
      baseNegative.push(...styleSpecific[style]);
    }

    return baseNegative.join(', ');
  }

  /**
   * 建议提示词改进
   */
  suggestImprovements(prompt) {
    const suggestions = [];
    const promptLower = prompt.toLowerCase();

    // 检查是否缺少质量词汇
    const hasQualityWords = this.qualityEnhancers.some(word => 
      promptLower.includes(word)
    );
    if (!hasQualityWords) {
      suggestions.push("添加质量增强词，如 'high quality', 'detailed'");
    }

    // 检查是否缺少风格描述
    const styleWords = ['realistic', 'cartoon', 'artistic', 'minimalist', 'professional'];
    const hasStyleWords = styleWords.some(word => promptLower.includes(word));
    if (!hasStyleWords) {
      suggestions.push("添加风格描述，如 'realistic', 'artistic'");
    }

    // 检查长度
    const wordCount = prompt.split(',').length;
    if (wordCount < 3) {
      suggestions.push('提示词过短，建议添加更多描述性词汇');
    } else if (wordCount > 20) {
      suggestions.push('提示词过长，建议精简到15个词汇以内');
    }

    // 检查是否有负面词汇
    const hasNegativeWords = this.negativePrompts.some(word => 
      promptLower.includes(word)
    );
    if (hasNegativeWords) {
      suggestions.push('移除负面词汇，使用正面描述');
    }

    return suggestions;
  }

  /**
   * 创建提示词变体
   */
  createVariations(basePrompt, count = 3) {
    const variations = [];

    // 不同风格变体
    const styles = ['realistic', 'artistic', 'professional'];
    for (let i = 0; i < Math.min(count, styles.length); i++) {
      const optimized = this.optimizePrompt({
        prompt: basePrompt,
        style: styles[i]
      });
      variations.push(optimized.optimized);
    }

    // 如果需要更多变体，添加颜色变体
    if (count > 3) {
      const colors = ['warm', 'cool', 'vibrant'];
      for (let i = 0; i < Math.min(count - 3, colors.length); i++) {
        const optimized = this.optimizePrompt({
          prompt: basePrompt,
          color_preference: colors[i]
        });
        variations.push(optimized.optimized);
      }
    }

    return variations.slice(0, count);
  }

  /**
   * 分析提示词复杂度
   */
  analyzeComplexity(prompt) {
    const words = prompt.split(/[,\s]+/).filter(word => word.length > 0);
    const uniqueWords = new Set(words.map(word => word.toLowerCase()));
    
    const analysis = {
      total_words: words.length,
      unique_words: uniqueWords.size,
      complexity: 'simple',
      readability: 'good',
      suggestions: []
    };

    // 复杂度评估
    if (words.length > 20) {
      analysis.complexity = 'complex';
      analysis.suggestions.push('考虑简化提示词');
    } else if (words.length > 10) {
      analysis.complexity = 'medium';
    }

    // 可读性评估
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    if (avgWordLength > 8) {
      analysis.readability = 'difficult';
      analysis.suggestions.push('使用更简单的词汇');
    } else if (avgWordLength < 4) {
      analysis.readability = 'too_simple';
      analysis.suggestions.push('添加更多描述性词汇');
    }

    return analysis;
  }

  /**
   * 获取推荐的提示词模板
   */
  getPromptTemplates(category = 'general') {
    const templates = {
      'general': [
        '{subject}, {style}, high quality, detailed',
        '{subject}, {mood} atmosphere, professional lighting',
        '{subject}, {color_scheme}, modern design, clean composition'
      ],
      'hero': [
        '{subject} hero banner, {style}, wide aspect ratio, professional',
        '{subject} background, {mood} atmosphere, high resolution',
        '{subject} landscape, {color_scheme}, cinematic lighting'
      ],
      'product': [
        '{subject} product photography, clean background, studio lighting',
        '{subject}, commercial style, high quality, detailed',
        '{subject} showcase, professional presentation, modern design'
      ],
      'icon': [
        '{subject} icon, simple design, {color} color, clean lines',
        '{subject} symbol, minimalist style, vector art',
        '{subject} pictogram, modern design, scalable'
      ]
    };

    return templates[category] || templates['general'];
  }
}
