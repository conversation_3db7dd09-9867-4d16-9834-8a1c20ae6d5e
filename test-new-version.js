/**
 * 测试新版本的环境变量处理
 */

import { spawn } from 'child_process';

// 模拟你的MCP配置环境变量
const mcpEnv = {
  MODELSCOPE_API_KEY: 'sk-your-real-api-key-here',
  MODELSCOPE_MODEL_ID: 'damo/text_to_image_synthesis-stable_diffusion_xl-base-1.0',
  MODELSCOPE_BASE_URL: 'https://your-custom-api-url.com',  // 测试自定义URL
  OUTPUT_DIR: './generated_images',
  MAX_IMAGE_SIZE: '2048'
};

async function testNewVersion() {
  console.log('🧪 测试新版本的环境变量处理...\n');
  
  console.log('📋 测试环境变量:');
  Object.entries(mcpEnv).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  console.log('');
  
  // 启动MCP服务器
  const server = spawn('node', ['build/index.js'], {
    env: { ...process.env, ...mcpEnv },
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let responseReceived = false;
  
  // 处理服务器响应
  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    
    lines.forEach(line => {
      try {
        const response = JSON.parse(line);
        console.log('📨 MCP服务器响应:');
        console.log(JSON.stringify(response, null, 2));
        responseReceived = true;
        
        setTimeout(() => {
          server.kill();
          process.exit(0);
        }, 1000);
      } catch (error) {
        // 忽略非JSON输出
      }
    });
  });
  
  // 处理服务器日志
  server.stderr.on('data', (data) => {
    console.log('🔍 服务器日志:', data.toString().trim());
  });
  
  // 等待服务器启动
  setTimeout(() => {
    console.log('📤 发送测试请求...\n');
    
    const request = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: 'generate_image',
        arguments: {
          prompt: 'test logo',
          width: 200,
          height: 80,
          imageType: 'logo'
        }
      }
    };
    
    server.stdin.write(JSON.stringify(request) + '\n');
  }, 2000);
  
  // 超时处理
  setTimeout(() => {
    if (!responseReceived) {
      console.log('\n⏰ 测试超时');
      server.kill();
      process.exit(1);
    }
  }, 10000);
}

// 运行测试
testNewVersion().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
