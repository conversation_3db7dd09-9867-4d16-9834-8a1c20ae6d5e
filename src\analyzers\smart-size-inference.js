/**
 * 智能尺寸推断算法
 * 基于代码上下文、布局需求和图片用途自动推断最佳图片尺寸
 */

import { Logger } from '../utils/logger.js';

export class SmartSizeInference {
  constructor() {
    this.logger = new Logger();
    
    // 预定义尺寸模板
    this.sizeTemplates = {
      // Hero区域尺寸
      hero: {
        desktop: { width: 1920, height: 1080, ratio: '16:9' },
        tablet: { width: 1024, height: 576, ratio: '16:9' },
        mobile: { width: 375, height: 667, ratio: '9:16' },
        banner: { width: 1200, height: 400, ratio: '3:1' },
        wide: { width: 1600, height: 600, ratio: '8:3' }
      },
      
      // Logo尺寸
      logo: {
        small: { width: 120, height: 40, ratio: '3:1' },
        medium: { width: 200, height: 80, ratio: '5:2' },
        large: { width: 300, height: 120, ratio: '5:2' },
        square: { width: 150, height: 150, ratio: '1:1' },
        favicon: { width: 32, height: 32, ratio: '1:1' }
      },
      
      // 图标尺寸
      icon: {
        tiny: { width: 16, height: 16, ratio: '1:1' },
        small: { width: 24, height: 24, ratio: '1:1' },
        medium: { width: 32, height: 32, ratio: '1:1' },
        large: { width: 48, height: 48, ratio: '1:1' },
        xlarge: { width: 64, height: 64, ratio: '1:1' },
        button: { width: 20, height: 20, ratio: '1:1' }
      },
      
      // 背景图尺寸
      background: {
        fullscreen: { width: 1920, height: 1080, ratio: '16:9' },
        section: { width: 1200, height: 800, ratio: '3:2' },
        card: { width: 400, height: 300, ratio: '4:3' },
        pattern: { width: 200, height: 200, ratio: '1:1' },
        texture: { width: 512, height: 512, ratio: '1:1' }
      },
      
      // 内容图片尺寸
      content: {
        article: { width: 800, height: 600, ratio: '4:3' },
        thumbnail: { width: 300, height: 200, ratio: '3:2' },
        gallery: { width: 600, height: 400, ratio: '3:2' },
        preview: { width: 400, height: 300, ratio: '4:3' },
        inline: { width: 500, height: 300, ratio: '5:3' }
      },
      
      // 产品图片尺寸
      product: {
        main: { width: 800, height: 800, ratio: '1:1' },
        thumbnail: { width: 200, height: 200, ratio: '1:1' },
        gallery: { width: 600, height: 600, ratio: '1:1' },
        zoom: { width: 1200, height: 1200, ratio: '1:1' },
        list: { width: 300, height: 300, ratio: '1:1' }
      },
      
      // 头像尺寸
      avatar: {
        tiny: { width: 32, height: 32, ratio: '1:1' },
        small: { width: 64, height: 64, ratio: '1:1' },
        medium: { width: 128, height: 128, ratio: '1:1' },
        large: { width: 256, height: 256, ratio: '1:1' },
        profile: { width: 200, height: 200, ratio: '1:1' }
      },
      
      // 横幅尺寸
      banner: {
        top: { width: 728, height: 90, ratio: '8:1' },
        side: { width: 300, height: 250, ratio: '6:5' },
        mobile: { width: 320, height: 50, ratio: '32:5' },
        leaderboard: { width: 728, height: 90, ratio: '8:1' },
        rectangle: { width: 300, height: 250, ratio: '6:5' }
      }
    };

    // CSS类名到尺寸的映射
    this.cssClassMappings = {
      // Hero相关
      'hero': 'hero.banner',
      'hero-section': 'hero.banner',
      'hero-banner': 'hero.banner',
      'hero-image': 'hero.desktop',
      'jumbotron': 'hero.banner',
      'masthead': 'hero.wide',
      
      // Logo相关
      'logo': 'logo.medium',
      'brand': 'logo.medium',
      'brand-logo': 'logo.large',
      'navbar-brand': 'logo.small',
      'header-logo': 'logo.medium',
      
      // 图标相关
      'icon': 'icon.medium',
      'fa': 'icon.small',
      'material-icons': 'icon.medium',
      'btn-icon': 'icon.button',
      'nav-icon': 'icon.small',
      
      // 背景相关
      'bg-image': 'background.section',
      'background': 'background.fullscreen',
      'bg-pattern': 'background.pattern',
      'bg-texture': 'background.texture',
      
      // 内容相关
      'content-image': 'content.article',
      'article-image': 'content.article',
      'thumbnail': 'content.thumbnail',
      'gallery-image': 'content.gallery',
      'preview-image': 'content.preview',
      
      // 产品相关
      'product-image': 'product.main',
      'product-thumb': 'product.thumbnail',
      'product-gallery': 'product.gallery',
      
      // 头像相关
      'avatar': 'avatar.medium',
      'profile-image': 'avatar.large',
      'user-avatar': 'avatar.small',
      
      // 横幅相关
      'banner': 'banner.top',
      'ad-banner': 'banner.leaderboard',
      'sidebar-banner': 'banner.side'
    };

    // HTML标签到尺寸的映射
    this.htmlTagMappings = {
      'img[class*="hero"]': 'hero.banner',
      'img[class*="logo"]': 'logo.medium',
      'img[class*="icon"]': 'icon.medium',
      'img[class*="avatar"]': 'avatar.medium',
      'img[class*="product"]': 'product.main',
      'img[class*="banner"]': 'banner.top',
      'img[class*="background"]': 'background.section',
      'img[class*="thumbnail"]': 'content.thumbnail'
    };
  }

  /**
   * 智能推断图片尺寸
   * @param {Object} options - 推断选项
   * @returns {Object} 推断结果
   */
  async inferSize(options = {}) {
    const {
      code_context = '',
      image_purpose = 'content',
      layout_info = '',
      responsive_needs = true,
      container_size = null,
      css_classes = '',
      html_tag = ''
    } = options;

    this.logger.info(`🔍 开始智能尺寸推断: ${image_purpose}`);

    try {
      // 1. 基于图片用途的基础推断
      let baseSizes = this.getBaseSizesByPurpose(image_purpose);

      // 2. 分析代码上下文
      const contextAnalysis = this.analyzeCodeContext(code_context);

      // 3. 分析CSS类名
      const cssAnalysis = this.analyzeCSSClasses(css_classes);

      // 4. 分析HTML标签
      const htmlAnalysis = this.analyzeHTMLTag(html_tag);

      // 5. 分析布局信息
      const layoutAnalysis = this.analyzeLayoutInfo(layout_info);

      // 6. 分析容器尺寸
      const containerAnalysis = this.analyzeContainerSize(container_size);

      // 7. 综合分析得出最佳尺寸
      const recommendedSizes = this.synthesizeRecommendations({
        baseSizes,
        contextAnalysis,
        cssAnalysis,
        htmlAnalysis,
        layoutAnalysis,
        containerAnalysis,
        responsive_needs
      });

      // 8. 生成响应式尺寸建议
      const responsiveSizes = responsive_needs ? 
        this.generateResponsiveSizes(recommendedSizes.primary) : null;

      const result = {
        success: true,
        primary: recommendedSizes.primary,
        alternatives: recommendedSizes.alternatives,
        responsive: responsiveSizes,
        analysis: {
          purpose: image_purpose,
          detected_context: contextAnalysis.detected,
          css_hints: cssAnalysis.hints,
          html_hints: htmlAnalysis.hints,
          layout_hints: layoutAnalysis.hints,
          confidence: recommendedSizes.confidence
        },
        recommendations: this.generateSizeRecommendations(recommendedSizes)
      };

      this.logger.info(`✅ 尺寸推断完成: ${result.primary.width}x${result.primary.height}`);
      return result;

    } catch (error) {
      this.logger.error('❌ 尺寸推断失败:', error);
      return {
        success: false,
        error: error.message,
        fallback: this.getFallbackSize(image_purpose)
      };
    }
  }

  /**
   * 基于图片用途获取基础尺寸
   */
  getBaseSizesByPurpose(purpose) {
    const purposeMap = {
      'hero': this.sizeTemplates.hero,
      'logo': this.sizeTemplates.logo,
      'icon': this.sizeTemplates.icon,
      'background': this.sizeTemplates.background,
      'content': this.sizeTemplates.content,
      'product': this.sizeTemplates.product,
      'avatar': this.sizeTemplates.avatar,
      'banner': this.sizeTemplates.banner
    };

    return purposeMap[purpose] || this.sizeTemplates.content;
  }

  /**
   * 分析代码上下文
   */
  analyzeCodeContext(codeContext) {
    const analysis = {
      detected: [],
      hints: [],
      confidence: 0
    };

    if (!codeContext) return analysis;

    const context = codeContext.toLowerCase();

    // 检测容器类型
    const containerPatterns = {
      'container-fluid': { hint: '全宽容器', impact: 'large' },
      'container': { hint: '固定宽度容器', impact: 'medium' },
      'col-': { hint: 'Bootstrap列', impact: 'responsive' },
      'grid': { hint: 'CSS Grid布局', impact: 'flexible' },
      'flex': { hint: 'Flexbox布局', impact: 'flexible' },
      'hero': { hint: 'Hero区域', impact: 'large' },
      'header': { hint: '页头区域', impact: 'medium' },
      'sidebar': { hint: '侧边栏', impact: 'small' },
      'card': { hint: '卡片组件', impact: 'medium' },
      'modal': { hint: '模态框', impact: 'medium' }
    };

    for (const [pattern, info] of Object.entries(containerPatterns)) {
      if (context.includes(pattern)) {
        analysis.detected.push(pattern);
        analysis.hints.push(info.hint);
        analysis.confidence += 0.1;
      }
    }

    // 检测尺寸相关的CSS属性
    const sizePatterns = {
      'width:\\s*(\\d+)px': 'width',
      'height:\\s*(\\d+)px': 'height',
      'max-width:\\s*(\\d+)px': 'maxWidth',
      'max-height:\\s*(\\d+)px': 'maxHeight'
    };

    for (const [pattern, property] of Object.entries(sizePatterns)) {
      const match = context.match(new RegExp(pattern));
      if (match) {
        analysis.detected.push(`${property}: ${match[1]}px`);
        analysis.confidence += 0.2;
      }
    }

    return analysis;
  }

  /**
   * 分析CSS类名
   */
  analyzeCSSClasses(cssClasses) {
    const analysis = {
      hints: [],
      mappedSize: null,
      confidence: 0
    };

    if (!cssClasses) return analysis;

    const classes = cssClasses.toLowerCase().split(/\s+/);

    for (const className of classes) {
      if (this.cssClassMappings[className]) {
        analysis.mappedSize = this.cssClassMappings[className];
        analysis.hints.push(`CSS类 "${className}" 映射到 ${analysis.mappedSize}`);
        analysis.confidence += 0.3;
        break;
      }

      // 模糊匹配
      for (const [pattern, mapping] of Object.entries(this.cssClassMappings)) {
        if (className.includes(pattern) || pattern.includes(className)) {
          analysis.mappedSize = mapping;
          analysis.hints.push(`CSS类 "${className}" 模糊匹配到 ${mapping}`);
          analysis.confidence += 0.2;
          break;
        }
      }
    }

    return analysis;
  }

  /**
   * 分析HTML标签
   */
  analyzeHTMLTag(htmlTag) {
    const analysis = {
      hints: [],
      mappedSize: null,
      confidence: 0
    };

    if (!htmlTag) return analysis;

    const tag = htmlTag.toLowerCase();

    for (const [pattern, mapping] of Object.entries(this.htmlTagMappings)) {
      if (tag.includes(pattern.replace(/\[.*\]/, ''))) {
        analysis.mappedSize = mapping;
        analysis.hints.push(`HTML标签匹配到 ${mapping}`);
        analysis.confidence += 0.2;
        break;
      }
    }

    return analysis;
  }

  /**
   * 分析布局信息
   */
  analyzeLayoutInfo(layoutInfo) {
    const analysis = {
      hints: [],
      layoutType: null,
      confidence: 0
    };

    if (!layoutInfo) return analysis;

    const info = layoutInfo.toLowerCase();

    const layoutPatterns = {
      'full.*width': { type: 'fullwidth', hint: '全宽布局' },
      'responsive': { type: 'responsive', hint: '响应式布局' },
      'mobile.*first': { type: 'mobile-first', hint: '移动优先' },
      'grid.*layout': { type: 'grid', hint: '网格布局' },
      'card.*layout': { type: 'card', hint: '卡片布局' },
      'sidebar': { type: 'sidebar', hint: '侧边栏布局' },
      'two.*column': { type: 'two-column', hint: '双列布局' },
      'three.*column': { type: 'three-column', hint: '三列布局' }
    };

    for (const [pattern, layoutData] of Object.entries(layoutPatterns)) {
      if (info.match(new RegExp(pattern))) {
        analysis.layoutType = layoutData.type;
        analysis.hints.push(layoutData.hint);
        analysis.confidence += 0.15;
      }
    }

    return analysis;
  }

  /**
   * 分析容器尺寸
   */
  analyzeContainerSize(containerSize) {
    const analysis = {
      hints: [],
      constraints: null,
      confidence: 0
    };

    if (!containerSize) return analysis;

    if (typeof containerSize === 'object') {
      const { width, height } = containerSize;
      
      if (width && height) {
        analysis.constraints = { width, height };
        analysis.hints.push(`容器尺寸约束: ${width}x${height}`);
        analysis.confidence += 0.4;
      }
    }

    return analysis;
  }

  /**
   * 综合分析生成推荐尺寸
   */
  synthesizeRecommendations(analyses) {
    const {
      baseSizes,
      contextAnalysis,
      cssAnalysis,
      htmlAnalysis,
      layoutAnalysis,
      containerAnalysis,
      responsive_needs
    } = analyses;

    let primarySize = null;
    let alternatives = [];
    let confidence = 0;

    // 优先级：CSS类映射 > HTML标签映射 > 容器约束 > 基础尺寸
    if (cssAnalysis.mappedSize) {
      primarySize = this.getSizeFromPath(cssAnalysis.mappedSize);
      confidence += cssAnalysis.confidence;
    } else if (htmlAnalysis.mappedSize) {
      primarySize = this.getSizeFromPath(htmlAnalysis.mappedSize);
      confidence += htmlAnalysis.confidence;
    } else if (containerAnalysis.constraints) {
      primarySize = containerAnalysis.constraints;
      confidence += containerAnalysis.confidence;
    } else {
      // 使用基础尺寸，根据上下文调整
      primarySize = this.selectBestBaseSize(baseSizes, contextAnalysis, layoutAnalysis);
      confidence += 0.3;
    }

    // 生成替代方案
    alternatives = this.generateAlternatives(primarySize, baseSizes);

    // 应用响应式调整
    if (responsive_needs) {
      primarySize = this.applyResponsiveAdjustments(primarySize, layoutAnalysis);
    }

    return {
      primary: primarySize,
      alternatives,
      confidence: Math.min(confidence, 1.0)
    };
  }

  /**
   * 从路径获取尺寸对象
   */
  getSizeFromPath(path) {
    const [category, size] = path.split('.');
    return this.sizeTemplates[category]?.[size] || this.sizeTemplates.content.article;
  }

  /**
   * 选择最佳基础尺寸
   */
  selectBestBaseSize(baseSizes, contextAnalysis, layoutAnalysis) {
    // 根据上下文分析选择合适的基础尺寸
    if (contextAnalysis.detected.includes('hero')) {
      return baseSizes.banner || baseSizes.desktop;
    }
    
    if (layoutAnalysis.layoutType === 'sidebar') {
      return baseSizes.small || baseSizes.medium;
    }
    
    if (layoutAnalysis.layoutType === 'fullwidth') {
      return baseSizes.large || baseSizes.desktop;
    }

    // 默认选择中等尺寸
    return baseSizes.medium || baseSizes.article || Object.values(baseSizes)[0];
  }

  /**
   * 生成替代尺寸方案
   */
  generateAlternatives(primarySize, baseSizes) {
    const alternatives = [];
    
    for (const [key, size] of Object.entries(baseSizes)) {
      if (size.width !== primarySize.width || size.height !== primarySize.height) {
        alternatives.push({
          name: key,
          ...size,
          description: `${key}尺寸方案`
        });
      }
    }

    return alternatives.slice(0, 3); // 最多返回3个替代方案
  }

  /**
   * 应用响应式调整
   */
  applyResponsiveAdjustments(size, layoutAnalysis) {
    if (layoutAnalysis.layoutType === 'mobile-first') {
      // 移动优先，适当减小尺寸
      return {
        width: Math.round(size.width * 0.8),
        height: Math.round(size.height * 0.8),
        ratio: size.ratio
      };
    }

    return size;
  }

  /**
   * 生成响应式尺寸
   */
  generateResponsiveSizes(primarySize) {
    const { width, height } = primarySize;
    
    return {
      desktop: { width, height },
      tablet: { 
        width: Math.round(width * 0.75), 
        height: Math.round(height * 0.75) 
      },
      mobile: { 
        width: Math.round(width * 0.5), 
        height: Math.round(height * 0.5) 
      }
    };
  }

  /**
   * 生成尺寸推荐说明
   */
  generateSizeRecommendations(recommendations) {
    const { primary, confidence } = recommendations;
    
    const tips = [];
    
    if (confidence > 0.7) {
      tips.push('高置信度推荐，基于代码上下文分析');
    } else if (confidence > 0.4) {
      tips.push('中等置信度推荐，建议根据实际需求调整');
    } else {
      tips.push('低置信度推荐，建议手动指定尺寸');
    }

    if (primary.ratio) {
      tips.push(`推荐宽高比: ${primary.ratio}`);
    }

    return tips;
  }

  /**
   * 获取备用尺寸
   */
  getFallbackSize(purpose) {
    const fallbacks = {
      hero: { width: 1200, height: 600, ratio: '2:1' },
      logo: { width: 200, height: 80, ratio: '5:2' },
      icon: { width: 32, height: 32, ratio: '1:1' },
      background: { width: 1920, height: 1080, ratio: '16:9' },
      content: { width: 800, height: 600, ratio: '4:3' },
      product: { width: 600, height: 600, ratio: '1:1' },
      avatar: { width: 128, height: 128, ratio: '1:1' },
      banner: { width: 728, height: 90, ratio: '8:1' }
    };

    return fallbacks[purpose] || fallbacks.content;
  }
}
