/**
 * 图片缓存管理器
 * 避免重复生成相同的图片
 */

import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { Logger } from './logger.js';

export class ImageCache {
  constructor(cacheDir = './cache') {
    this.logger = new Logger();
    this.cacheDir = path.resolve(cacheDir);
    this.maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
    
    // 确保缓存目录存在
    fs.ensureDirSync(this.cacheDir);
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(prompt, width, height, style) {
    const content = `${prompt}_${width}_${height}_${style}`;
    return crypto.createHash('md5').update(content).digest('hex');
  }

  /**
   * 获取缓存的图片
   */
  async getCachedImage(cacheKey) {
    try {
      const cacheFile = path.join(this.cacheDir, `${cacheKey}.png`);
      const metaFile = path.join(this.cacheDir, `${cacheKey}.meta.json`);

      if (await fs.pathExists(cacheFile) && await fs.pathExists(metaFile)) {
        const meta = await fs.readJson(metaFile);
        
        // 检查是否过期
        if (Date.now() - meta.created < this.maxAge) {
          this.logger.info(`📦 使用缓存图片: ${cacheKey}`);
          return {
            path: cacheFile,
            meta: meta
          };
        } else {
          // 删除过期缓存
          await this.deleteCachedImage(cacheKey);
        }
      }

      return null;
    } catch (error) {
      this.logger.error('❌ 获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 缓存图片
   */
  async cacheImage(cacheKey, imagePath, metadata = {}) {
    try {
      const cacheFile = path.join(this.cacheDir, `${cacheKey}.png`);
      const metaFile = path.join(this.cacheDir, `${cacheKey}.meta.json`);

      // 复制图片文件
      await fs.copy(imagePath, cacheFile);

      // 保存元数据
      const meta = {
        created: Date.now(),
        original_path: imagePath,
        cache_key: cacheKey,
        ...metadata
      };

      await fs.writeJson(metaFile, meta);

      this.logger.info(`💾 图片已缓存: ${cacheKey}`);
      return cacheFile;

    } catch (error) {
      this.logger.error('❌ 缓存图片失败:', error);
      throw error;
    }
  }

  /**
   * 删除缓存图片
   */
  async deleteCachedImage(cacheKey) {
    try {
      const cacheFile = path.join(this.cacheDir, `${cacheKey}.png`);
      const metaFile = path.join(this.cacheDir, `${cacheKey}.meta.json`);

      await Promise.all([
        fs.remove(cacheFile).catch(() => {}),
        fs.remove(metaFile).catch(() => {})
      ]);

      this.logger.debug(`🗑️  删除缓存: ${cacheKey}`);
    } catch (error) {
      this.logger.error('❌ 删除缓存失败:', error);
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanupExpiredCache() {
    try {
      const files = await fs.readdir(this.cacheDir);
      const metaFiles = files.filter(file => file.endsWith('.meta.json'));
      
      let cleanedCount = 0;

      for (const metaFile of metaFiles) {
        const metaPath = path.join(this.cacheDir, metaFile);
        const meta = await fs.readJson(metaPath);
        
        if (Date.now() - meta.created > this.maxAge) {
          const cacheKey = meta.cache_key;
          await this.deleteCachedImage(cacheKey);
          cleanedCount++;
        }
      }

      this.logger.info(`🧹 清理了 ${cleanedCount} 个过期缓存`);
      return cleanedCount;

    } catch (error) {
      this.logger.error('❌ 清理缓存失败:', error);
      return 0;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    try {
      const files = await fs.readdir(this.cacheDir);
      const imageFiles = files.filter(file => file.endsWith('.png'));
      const metaFiles = files.filter(file => file.endsWith('.meta.json'));

      let totalSize = 0;
      for (const file of imageFiles) {
        const filePath = path.join(this.cacheDir, file);
        const stats = await fs.stat(filePath);
        totalSize += stats.size;
      }

      return {
        total_images: imageFiles.length,
        total_meta: metaFiles.length,
        total_size_bytes: totalSize,
        total_size_mb: Math.round(totalSize / (1024 * 1024) * 100) / 100,
        cache_directory: this.cacheDir
      };

    } catch (error) {
      this.logger.error('❌ 获取缓存统计失败:', error);
      return {
        total_images: 0,
        total_meta: 0,
        total_size_bytes: 0,
        total_size_mb: 0,
        cache_directory: this.cacheDir
      };
    }
  }
}
