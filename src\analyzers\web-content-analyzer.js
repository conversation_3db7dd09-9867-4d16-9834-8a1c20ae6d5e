/**
 * 网页内容分析器 (Node.js版本)
 * 智能分析网页内容并推荐合适的图片类型和风格
 */

import { Logger } from '../utils/logger.js';

// 枚举定义
export const WebsiteType = {
  CORPORATE: 'corporate',
  ECOMMERCE: 'ecommerce',
  BLOG: 'blog',
  PORTFOLIO: 'portfolio',
  LANDING_PAGE: 'landing_page',
  EDUCATIONAL: 'educational',
  NEWS: 'news',
  SOCIAL: 'social',
  TECH: 'tech',
  CREATIVE: 'creative'
};

export const Industry = {
  TECHNOLOGY: 'technology',
  FINANCE: 'finance',
  HEALTHCARE: 'healthcare',
  EDUCATION: 'education',
  RETAIL: 'retail',
  FOOD: 'food',
  TRAVEL: 'travel',
  REAL_ESTATE: 'real_estate',
  FASHION: 'fashion',
  AUTOMOTIVE: 'automotive',
  ENTERTAINMENT: 'entertainment',
  SPORTS: 'sports'
};

export class WebContentAnalyzer {
  constructor() {
    this.logger = new Logger();
    
    // 网站类型关键词
    this.websiteKeywords = {
      [WebsiteType.CORPORATE]: [
        'company', 'corporation', 'business', 'enterprise', 'services',
        'about us', 'team', 'mission', 'vision', '公司', '企业', '商务'
      ],
      [WebsiteType.ECOMMERCE]: [
        'shop', 'store', 'buy', 'cart', 'product', 'price', 'order',
        'checkout', '商店', '购买', '产品', '价格', '订单'
      ],
      [WebsiteType.BLOG]: [
        'blog', 'article', 'post', 'news', 'story', 'read more',
        '博客', '文章', '新闻', '故事'
      ],
      [WebsiteType.PORTFOLIO]: [
        'portfolio', 'work', 'projects', 'gallery', 'showcase',
        '作品集', '项目', '展示', '画廊'
      ],
      [WebsiteType.LANDING_PAGE]: [
        'landing', 'signup', 'register', 'download', 'free trial',
        'get started', '注册', '下载', '免费试用', '开始'
      ],
      [WebsiteType.EDUCATIONAL]: [
        'education', 'course', 'learn', 'tutorial', 'training',
        '教育', '课程', '学习', '教程', '培训'
      ]
    };

    // 行业关键词
    this.industryKeywords = {
      [Industry.TECHNOLOGY]: [
        'tech', 'software', 'app', 'digital', 'AI', 'cloud', 'data',
        '科技', '软件', '应用', '数字', '人工智能', '云', '数据'
      ],
      [Industry.FINANCE]: [
        'finance', 'bank', 'investment', 'money', 'loan', 'credit',
        '金融', '银行', '投资', '资金', '贷款', '信贷'
      ],
      [Industry.HEALTHCARE]: [
        'health', 'medical', 'doctor', 'hospital', 'care', 'treatment',
        '健康', '医疗', '医生', '医院', '护理', '治疗'
      ],
      [Industry.EDUCATION]: [
        'education', 'school', 'university', 'course', 'learning', 'student',
        '教育', '学校', '大学', '课程', '学习', '学生'
      ],
      [Industry.RETAIL]: [
        'retail', 'fashion', 'clothing', 'style', 'brand', 'collection',
        '零售', '时尚', '服装', '风格', '品牌', '系列'
      ],
      [Industry.FOOD]: [
        'food', 'restaurant', 'cuisine', 'recipe', 'cooking', 'dining',
        '食物', '餐厅', '美食', '食谱', '烹饪', '用餐'
      ]
    };

    // 颜色方案关键词
    this.colorKeywords = {
      'blue': ['blue', 'navy', 'azure', 'cyan', '蓝色', '海蓝', '天蓝'],
      'red': ['red', 'crimson', 'scarlet', 'burgundy', '红色', '深红', '酒红'],
      'green': ['green', 'emerald', 'forest', 'lime', '绿色', '翠绿', '森林绿'],
      'purple': ['purple', 'violet', 'lavender', 'plum', '紫色', '紫罗兰', '薰衣草'],
      'orange': ['orange', 'amber', 'coral', 'peach', '橙色', '琥珀', '珊瑚'],
      'yellow': ['yellow', 'gold', 'golden', 'sunshine', '黄色', '金色', '阳光'],
      'black': ['black', 'dark', 'charcoal', 'midnight', '黑色', '深色', '炭黑'],
      'white': ['white', 'light', 'clean', 'pure', '白色', '浅色', '纯白'],
      'gray': ['gray', 'grey', 'silver', 'neutral', '灰色', '银色', '中性']
    };

    // 情绪关键词
    this.moodKeywords = {
      'professional': ['professional', 'business', 'corporate', 'formal', '专业', '商务', '正式'],
      'friendly': ['friendly', 'warm', 'welcoming', 'approachable', '友好', '温暖', '亲切'],
      'modern': ['modern', 'contemporary', 'sleek', 'cutting-edge', '现代', '当代', '时尚'],
      'creative': ['creative', 'artistic', 'innovative', 'unique', '创意', '艺术', '创新'],
      'trustworthy': ['trust', 'reliable', 'secure', 'safe', '信任', '可靠', '安全'],
      'energetic': ['energetic', 'dynamic', 'vibrant', 'active', '充满活力', '动态', '活跃']
    };

    // HTML结构分析模式
    this.htmlPatterns = {
      'hero-section': /<(?:section|div)[^>]*class[^>]*hero[^>]*>/i,
      'navigation': /<(?:nav|ul)[^>]*class[^>]*nav[^>]*>/i,
      'header': /<header[^>]*>/i,
      'footer': /<footer[^>]*>/i,
      'sidebar': /<(?:aside|div)[^>]*class[^>]*sidebar[^>]*>/i,
      'card': /<(?:div|article)[^>]*class[^>]*card[^>]*>/i,
      'grid': /<(?:div|section)[^>]*class[^>]*(?:grid|row|col)[^>]*>/i,
      'container': /<(?:div|section)[^>]*class[^>]*container[^>]*>/i
    };
  }

  /**
   * 分析网页内容
   * @param {Object} options - 分析选项
   * @returns {Object} 分析结果
   */
  async analyzeContent(options = {}) {
    const {
      page_content = '',
      page_title = '',
      meta_description = '',
      html_structure = '',
      css_classes = '',
      additional_context = ''
    } = options;

    this.logger.info('🔍 开始分析网页内容...');

    try {
      // 合并所有文本进行分析
      const fullText = `${page_title} ${meta_description} ${page_content} ${additional_context}`.toLowerCase();

      // 分析网站类型
      const websiteType = this.detectWebsiteType(fullText);

      // 分析行业
      const industry = this.detectIndustry(fullText);

      // 分析颜色方案
      const colorScheme = this.detectColorScheme(fullText, css_classes);

      // 分析情绪/氛围
      const mood = this.detectMood(fullText);

      // 分析HTML结构
      const structureAnalysis = this.analyzeHTMLStructure(html_structure);

      // 推断目标受众
      const targetAudience = this.inferTargetAudience(websiteType, industry, fullText);

      // 推荐风格
      const recommendedStyles = this.recommendStyles(websiteType, industry, mood);

      // 生成图片建议
      const imageSuggestions = this.generateImageSuggestions({
        websiteType,
        industry,
        colorScheme,
        mood,
        structureAnalysis
      });

      const result = {
        success: true,
        analysis: {
          website_type: websiteType,
          industry: industry,
          color_scheme: colorScheme,
          mood: mood,
          target_audience: targetAudience,
          recommended_styles: recommendedStyles,
          structure_analysis: structureAnalysis
        },
        image_suggestions: imageSuggestions,
        input_summary: {
          page_title: page_title,
          content_length: page_content.length,
          has_html_structure: !!html_structure,
          has_css_classes: !!css_classes,
          has_meta_description: !!meta_description
        }
      };

      this.logger.info(`✅ 网页分析完成: ${websiteType} - ${industry}`);
      return result;

    } catch (error) {
      this.logger.error('❌ 网页分析失败:', error);
      return {
        success: false,
        error: error.message,
        fallback: this.getFallbackAnalysis()
      };
    }
  }

  /**
   * 检测网站类型
   */
  detectWebsiteType(text) {
    const scores = {};
    
    for (const [siteType, keywords] of Object.entries(this.websiteKeywords)) {
      const score = keywords.reduce((sum, keyword) => {
        return sum + (text.includes(keyword) ? 1 : 0);
      }, 0);
      
      if (score > 0) {
        scores[siteType] = score;
      }
    }

    if (Object.keys(scores).length === 0) {
      return WebsiteType.CORPORATE; // 默认
    }

    return Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
  }

  /**
   * 检测行业类型
   */
  detectIndustry(text) {
    const scores = {};
    
    for (const [industry, keywords] of Object.entries(this.industryKeywords)) {
      const score = keywords.reduce((sum, keyword) => {
        return sum + (text.includes(keyword) ? 1 : 0);
      }, 0);
      
      if (score > 0) {
        scores[industry] = score;
      }
    }

    if (Object.keys(scores).length === 0) {
      return Industry.TECHNOLOGY; // 默认
    }

    return Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
  }

  /**
   * 检测颜色方案
   */
  detectColorScheme(text, cssClasses = '') {
    const detectedColors = [];
    const combinedText = `${text} ${cssClasses}`.toLowerCase();

    for (const [color, keywords] of Object.entries(this.colorKeywords)) {
      if (keywords.some(keyword => combinedText.includes(keyword))) {
        detectedColors.push(color);
      }
    }

    // 如果没有检测到颜色，根据行业推荐默认颜色
    if (detectedColors.length === 0) {
      const industry = this.detectIndustry(text);
      const industryColors = {
        [Industry.TECHNOLOGY]: ['blue', 'gray'],
        [Industry.FINANCE]: ['blue', 'green'],
        [Industry.HEALTHCARE]: ['blue', 'white'],
        [Industry.EDUCATION]: ['blue', 'orange'],
        [Industry.RETAIL]: ['red', 'black'],
        [Industry.FOOD]: ['orange', 'red'],
        [Industry.TRAVEL]: ['blue', 'green']
      };
      
      return industryColors[industry] || ['blue', 'gray'];
    }

    return detectedColors.slice(0, 3); // 最多返回3种颜色
  }

  /**
   * 检测情绪/氛围
   */
  detectMood(text) {
    const scores = {};
    
    for (const [mood, keywords] of Object.entries(this.moodKeywords)) {
      const score = keywords.reduce((sum, keyword) => {
        return sum + (text.includes(keyword) ? 1 : 0);
      }, 0);
      
      if (score > 0) {
        scores[mood] = score;
      }
    }

    if (Object.keys(scores).length === 0) {
      return 'professional'; // 默认
    }

    return Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
  }

  /**
   * 分析HTML结构
   */
  analyzeHTMLStructure(htmlStructure) {
    const analysis = {
      detected_elements: [],
      layout_hints: [],
      complexity: 'simple'
    };

    if (!htmlStructure) return analysis;

    const html = htmlStructure.toLowerCase();

    // 检测结构元素
    for (const [element, pattern] of Object.entries(this.htmlPatterns)) {
      if (pattern.test(htmlStructure)) {
        analysis.detected_elements.push(element);
      }
    }

    // 分析布局复杂度
    const complexityIndicators = [
      'grid', 'flex', 'container', 'row', 'col', 'card', 'modal', 'carousel'
    ];

    const complexityScore = complexityIndicators.reduce((score, indicator) => {
      return score + (html.includes(indicator) ? 1 : 0);
    }, 0);

    if (complexityScore > 5) {
      analysis.complexity = 'complex';
    } else if (complexityScore > 2) {
      analysis.complexity = 'medium';
    }

    // 生成布局提示
    if (analysis.detected_elements.includes('hero-section')) {
      analysis.layout_hints.push('需要大尺寸hero图片');
    }
    
    if (analysis.detected_elements.includes('grid')) {
      analysis.layout_hints.push('网格布局，需要统一尺寸的图片');
    }
    
    if (analysis.detected_elements.includes('card')) {
      analysis.layout_hints.push('卡片布局，需要中等尺寸的图片');
    }

    return analysis;
  }

  /**
   * 推断目标受众
   */
  inferTargetAudience(websiteType, industry, text) {
    const audienceMap = {
      [`${WebsiteType.CORPORATE}_${Industry.TECHNOLOGY}`]: '技术专业人士和企业决策者',
      [`${WebsiteType.ECOMMERCE}_${Industry.RETAIL}`]: '在线购物者和时尚爱好者',
      [`${WebsiteType.BLOG}_${Industry.EDUCATION}`]: '学习者和教育工作者',
      [`${WebsiteType.PORTFOLIO}_${Industry.CREATIVE}`]: '潜在客户和创意专业人士',
      [`${WebsiteType.LANDING_PAGE}_${Industry.TECHNOLOGY}`]: '潜在用户和早期采用者'
    };

    const key = `${websiteType}_${industry}`;
    return audienceMap[key] || '一般用户和潜在客户';
  }

  /**
   * 推荐风格
   */
  recommendStyles(websiteType, industry, mood) {
    const styleRecommendations = {
      [WebsiteType.CORPORATE]: ['professional', 'minimalist'],
      [WebsiteType.ECOMMERCE]: ['realistic', 'professional'],
      [WebsiteType.BLOG]: ['realistic', 'artistic'],
      [WebsiteType.PORTFOLIO]: ['artistic', 'creative'],
      [WebsiteType.LANDING_PAGE]: ['modern', 'minimalist']
    };

    const industryStyles = {
      [Industry.TECHNOLOGY]: ['modern', 'minimalist'],
      [Industry.FINANCE]: ['professional', 'trustworthy'],
      [Industry.HEALTHCARE]: ['clean', 'professional'],
      [Industry.EDUCATION]: ['friendly', 'approachable'],
      [Industry.RETAIL]: ['attractive', 'lifestyle']
    };

    // 合并推荐
    let styles = styleRecommendations[websiteType] || ['professional'];
    styles = styles.concat(industryStyles[industry] || []);

    // 根据情绪调整
    if (mood === 'creative') {
      styles.push('artistic');
    } else if (mood === 'energetic') {
      styles.push('vibrant');
    }

    // 去重并返回前3个
    return [...new Set(styles)].slice(0, 3);
  }

  /**
   * 生成图片建议
   */
  generateImageSuggestions(options) {
    const { websiteType, industry, colorScheme, mood, structureAnalysis } = options;
    const suggestions = [];

    // 根据网站类型生成建议
    if (websiteType === WebsiteType.CORPORATE) {
      suggestions.push({
        type: 'hero-banner',
        description: '专业的企业主页横幅',
        prompt: `professional corporate office, ${mood} atmosphere, ${colorScheme.slice(0, 2).join(' and ')} color scheme`,
        size_category: 'hero',
        priority: 'high'
      });

      suggestions.push({
        type: 'content-image',
        description: '团队合作场景',
        prompt: `business team collaboration, professional environment, ${mood} mood`,
        size_category: 'content',
        priority: 'medium'
      });
    }

    if (websiteType === WebsiteType.ECOMMERCE) {
      suggestions.push({
        type: 'hero-banner',
        description: '产品展示横幅',
        prompt: `product showcase, commercial photography, ${colorScheme.slice(0, 2).join(' and ')} background`,
        size_category: 'hero',
        priority: 'high'
      });

      suggestions.push({
        type: 'product',
        description: '产品特写图',
        prompt: 'product photography, clean background, professional lighting',
        size_category: 'product',
        priority: 'high'
      });
    }

    // 根据HTML结构添加建议
    if (structureAnalysis.detected_elements.includes('hero-section')) {
      suggestions.push({
        type: 'hero-image',
        description: 'Hero区域背景图',
        prompt: `${industry} hero background, ${mood} style, high quality`,
        size_category: 'hero',
        priority: 'high'
      });
    }

    if (structureAnalysis.detected_elements.includes('card')) {
      suggestions.push({
        type: 'card-image',
        description: '卡片组件图片',
        prompt: `${industry} card image, ${mood} style, clean design`,
        size_category: 'content',
        priority: 'medium'
      });
    }

    // 添加通用建议
    suggestions.push({
      type: 'icon',
      description: '功能图标',
      prompt: `simple icon, ${mood} style, ${colorScheme[0] || 'blue'} color`,
      size_category: 'icon',
      priority: 'low'
    });

    return suggestions.slice(0, 5); // 最多返回5个建议
  }

  /**
   * 获取备用分析结果
   */
  getFallbackAnalysis() {
    return {
      website_type: WebsiteType.CORPORATE,
      industry: Industry.TECHNOLOGY,
      color_scheme: ['blue', 'gray'],
      mood: 'professional',
      target_audience: '一般用户',
      recommended_styles: ['professional', 'modern'],
      image_suggestions: [{
        type: 'content-image',
        description: '通用内容图片',
        prompt: 'professional content image, clean design',
        size_category: 'content',
        priority: 'medium'
      }]
    };
  }
}
