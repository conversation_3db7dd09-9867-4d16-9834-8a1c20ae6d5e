/**
 * 日志工具类
 * 提供统一的日志记录功能
 */

export class Logger {
  constructor(debug = false) {
    this.debug = debug || process.env.DEBUG === 'true';
    this.logLevel = process.env.LOG_LEVEL || 'INFO';
  }

  /**
   * 格式化日志消息
   */
  formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] ${level}: ${message}${formattedArgs}`;
  }

  /**
   * 检查是否应该记录此级别的日志
   */
  shouldLog(level) {
    const levels = {
      'DEBUG': 0,
      'INFO': 1,
      'WARN': 2,
      'ERROR': 3
    };

    const currentLevel = levels[this.logLevel] || 1;
    const messageLevel = levels[level] || 1;

    return messageLevel >= currentLevel;
  }

  /**
   * 调试日志
   */
  debug(message, ...args) {
    if (this.debug && this.shouldLog('DEBUG')) {
      console.log(this.formatMessage('DEBUG', message, ...args));
    }
  }

  /**
   * 信息日志
   */
  info(message, ...args) {
    if (this.shouldLog('INFO')) {
      console.log(this.formatMessage('INFO', message, ...args));
    }
  }

  /**
   * 警告日志
   */
  warn(message, ...args) {
    if (this.shouldLog('WARN')) {
      console.warn(this.formatMessage('WARN', message, ...args));
    }
  }

  /**
   * 错误日志
   */
  error(message, ...args) {
    if (this.shouldLog('ERROR')) {
      console.error(this.formatMessage('ERROR', message, ...args));
    }
  }
}
