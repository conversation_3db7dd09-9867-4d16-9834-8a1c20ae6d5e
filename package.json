{"name": "mcp-image-generator", "version": "1.0.0", "description": "智能图片生成MCP工具 - 基于魔搭平台API，支持智能尺寸推断和内容分析", "main": "build/index.js", "type": "module", "scripts": {"start": "node build/index.js", "dev": "node --inspect build/index.js", "build": "robocopy src build /E /NFL /NDL /NJH /NJS || echo Build completed", "postbuild": "echo 'Build completed. Use: node build/index.js'", "mcp:stdio": "node build/index.js", "mcp:sse": "node build/index.js --transport=sse"}, "keywords": ["mcp", "model-context-protocol", "image-generation", "ai", "modelscope", "text-to-image", "智能图片生成", "魔搭平台"], "author": "MCP Image Generator Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/mcp-image-generator.git"}, "bugs": {"url": "https://github.com/your-username/mcp-image-generator/issues"}, "homepage": "https://github.com/your-username/mcp-image-generator#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.6.0", "dotenv": "^16.3.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "crypto": "^1.0.1", "url": "^0.11.3", "util": "^0.12.5"}, "devDependencies": {"@types/node": "^20.0.0", "eslint": "^8.50.0", "prettier": "^3.0.0", "nodemon": "^3.0.0"}, "files": ["src/", "bin/", "README.md", "LICENSE", ".env.example"], "preferGlobal": true, "publishConfig": {"access": "public"}, "mcp": {"name": "image-generator", "description": "智能图片生成工具，支持自动尺寸推断和内容分析", "version": "1.0.0", "tools": ["generate_image", "analyze_and_generate", "analyze_webpage_content", "optimize_prompt", "batch_generate_images", "get_generation_history", "list_supported_types", "get_image_suggestions", "smart_size_inference"]}}