{"name": "zhihui-mcp", "version": "1.0.0", "description": "智慧图片生成MCP工具 - 基于魔搭平台API，为网页开发提供智能图片生成服务", "main": "build/index.js", "type": "module", "bin": {"zhihui-mcp": "./build/index.js"}, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "start": "node build/index.js", "dev": "tsc && node build/index.js", "test": "node test.js"}, "files": ["build", "README.md"], "keywords": ["mcp", "model-context-protocol", "image-generation", "ai", "modelscope", "text-to-image", "智能图片生成", "魔搭平台", "网页开发"], "author": "zhihui-mcp team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/zhihui-mcp.git"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.4.0", "axios": "^1.6.0", "crypto": "^1.0.1", "fs-extra": "^11.2.0", "zod": "^3.23.8"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^22.10.0", "typescript": "^5.7.2"}, "mcp": {"name": "zhihui-image-generator", "description": "智慧图片生成工具，支持网页开发中的自动图片生成和智能尺寸适配", "version": "1.0.0", "tools": ["generate_image", "analyze_and_generate", "smart_size_inference", "batch_generate_images", "get_image_suggestions", "list_supported_models", "get_generation_history"]}}