# 快速开始指南

## 5分钟快速体验MCP智能图片生成工具

### 第一步：环境准备

1. **安装Python 3.11+**
   ```bash
   python --version  # 确保版本 >= 3.11
   ```

2. **安装uv包管理器（推荐）**
   ```bash
   # Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   
   # macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

3. **克隆项目**
   ```bash
   git clone https://github.com/your-username/mcp-image-generator.git
   cd mcp-image-generator
   ```

### 第二步：安装依赖

```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate.bat
# macOS/Linux
source .venv/bin/activate

# 安装依赖
uv add "mcp[cli]" httpx requests pillow python-dotenv
```

### 第三步：配置API密钥

1. **获取魔搭平台API密钥**
   - 访问 [魔搭平台](https://www.modelscope.cn/)
   - 注册账号并获取API密钥

2. **创建配置文件**
   ```bash
   cp .env.example .env
   ```

3. **编辑.env文件**
   ```env
   MODELSCOPE_API_KEY=your_api_key_here
   OUTPUT_DIR=./generated_images
   DEBUG=false
   ```

### 第四步：测试运行

1. **启动MCP Inspector调试**
   ```bash
   mcp dev image_generator.py
   ```

2. **在浏览器中打开显示的地址**（通常是 http://localhost:3000）

3. **点击"Connect"连接服务器**

4. **测试工具功能**
   - 切换到"Tools"标签
   - 点击"List Tools"查看可用工具
   - 选择"generate_image"工具
   - 输入测试参数：
     ```json
     {
       "prompt": "a beautiful sunset over mountains",
       "width": 512,
       "height": 512,
       "style": "realistic"
     }
     ```
   - 点击"Call Tool"执行

### 第五步：在AI工具中使用

#### Claude Desktop配置

1. **打开Claude Desktop配置**
   - 点击设置 → Developer → Edit Config

2. **添加MCP服务器配置**
   ```json
   {
     "mcpServers": {
       "image-generator": {
         "command": "python",
         "args": ["D:/path/to/your/project/image_generator.py"],
         "env": {
           "MODELSCOPE_API_KEY": "your_api_key_here"
         }
       }
     }
   }
   ```

3. **重启Claude Desktop**

4. **测试使用**
   - 在对话中输入："请生成一张现代办公室的图片"
   - Claude会自动调用MCP工具生成图片

#### Cursor IDE配置

1. **打开Cursor设置**
   - 按 `Ctrl+,` 打开设置
   - 搜索"MCP"

2. **添加配置**
   ```json
   {
     "mcpServers": {
       "image-generator": {
         "command": "python",
         "args": ["path/to/image_generator.py"],
         "env": {
           "MODELSCOPE_API_KEY": "your_api_key_here"
         }
       }
     }
   }
   ```

#### Cline插件配置

1. **安装Cline插件**
   - 在VSCode扩展市场搜索"Cline"
   - 安装并重启VSCode

2. **配置MCP服务器**
   - 点击Cline界面的MCP Server按钮
   - 选择"Configure MCP Servers"
   - 添加配置：
   ```json
   {
     "mcpServers": {
       "image_generator": {
         "command": "python",
         "args": ["D:/path/to/your/project/image_generator.py"],
         "env": {},
         "disabled": false,
         "autoApprove": []
       }
     }
   }
   ```

### 第六步：实际应用示例

#### 示例1：网站开发助手

```
用户：请帮我创建一个科技公司的网站首页，包含合适的图片

AI助手：我来为您创建一个科技公司网站首页，并生成合适的图片。

[AI调用analyze_webpage_content工具分析需求]
[AI调用analyze_and_generate工具生成hero banner]
[AI调用generate_image工具生成其他配图]

生成的网站代码：
<!DOCTYPE html>
<html>
<head>
    <title>创新科技 - 引领未来</title>
</head>
<body>
    <header class="hero">
        <h1>创新科技解决方案</h1>
        <img src="./generated_images/tech_hero_banner.png" alt="科技横幅">
    </header>
    <!-- 更多内容... -->
</body>
</html>
```

#### 示例2：博客文章配图

```
用户：写一篇关于人工智能发展的博客文章，需要配图

AI助手：我来为您写一篇AI发展的文章并配上合适的图片。

[AI调用analyze_and_generate工具生成文章配图]

# 人工智能的未来发展趋势

![AI发展趋势](./generated_images/ai_future_trends.png)

人工智能技术正在以前所未有的速度发展...
```

#### 示例3：产品展示页面

```
用户：为我的智能手表产品创建一个展示页面

AI助手：我来为您的智能手表创建产品展示页面。

[AI调用analyze_and_generate工具，content_type="product"]

<div class="product-showcase">
    <h2>智能手表 Pro</h2>
    <img src="./generated_images/smartwatch_product.png" alt="智能手表">
    <p>功能强大的智能穿戴设备，健康监测，运动追踪，智能通知。</p>
</div>
```

### 常见问题快速解决

#### Q: 图片生成失败怎么办？
A: 
1. 检查API密钥是否正确
2. 确认网络连接正常
3. 尝试使用更简单的提示词
4. 查看控制台错误信息

#### Q: MCP服务器连接不上？
A: 
1. 确认Python环境正确
2. 检查文件路径是否正确
3. 验证所有依赖包已安装
4. 尝试重启AI工具

#### Q: 生成的图片质量不好？
A: 
1. 使用optimize_prompt工具优化提示词
2. 尝试不同的风格参数
3. 增加图片尺寸
4. 添加更详细的描述

#### Q: 如何批量生成图片？
A: 使用batch_generate_images工具：
```json
{
  "prompts": "modern office\nbeautiful landscape\ntech logo",
  "width": 512,
  "height": 512,
  "style": "professional"
}
```

### 下一步

1. **探索高级功能**
   - 尝试不同的图片风格
   - 使用网页内容分析功能
   - 体验智能提示词优化

2. **自定义配置**
   - 调整输出目录
   - 设置默认图片尺寸
   - 配置调试模式

3. **集成到工作流**
   - 在项目中使用MCP工具
   - 自动化图片生成流程
   - 与其他AI工具配合使用

4. **贡献和反馈**
   - 提交使用反馈
   - 报告问题和建议
   - 参与项目开发

### 获取帮助

- 📖 查看完整文档：[README.md](README.md)
- 🐛 报告问题：[GitHub Issues](https://github.com/your-username/mcp-image-generator/issues)
- 💬 讨论交流：[GitHub Discussions](https://github.com/your-username/mcp-image-generator/discussions)
- 📧 联系开发者：<EMAIL>

恭喜！您已经成功设置并开始使用MCP智能图片生成工具。现在可以在各种AI助手中享受自动图片生成的便利了！
