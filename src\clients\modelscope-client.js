/**
 * 魔搭平台API客户端 (Node.js版本)
 * 支持文生图模型调用和多重备用方案
 */

import axios from 'axios';
import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { Logger } from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export class ModelScopeClient {
  constructor() {
    this.logger = new Logger();
    this.apiKey = process.env.MODELSCOPE_API_KEY;
    
    if (!this.apiKey) {
      this.logger.warn('⚠️  未设置MODELSCOPE_API_KEY，将使用备用方案');
    }

    // 使用新的魔搭平台API地址
    this.baseUrl = 'https://api-inference.modelscope.cn/v1';
    
    this.headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'User-Agent': 'MCP-Image-Generator/1.0.0'
    };

    // 创建输出目录
    this.outputDir = path.resolve(process.env.OUTPUT_DIR || './generated_images');
    fs.ensureDirSync(this.outputDir);

    // 配置axios实例
    this.httpClient = axios.create({
      timeout: 120000, // 2分钟超时
      headers: this.headers
    });

    // 可用的文生图模型列表
    this.availableModels = [
      'damo/cv_diffusion_text-to-image_synthesis',
      'damo/multi-modal_team-vit-large-patch14_multi-modal-similarity',
      'AI-ModelScope/stable-diffusion-v1-5',
      'damo/cv_unet_person-image-cartoon_compound-models'
    ];

    this.defaultModel = this.availableModels[0];
  }

  /**
   * 生成图片
   * @param {Object} options - 生成选项
   * @returns {Object} 生成结果
   */
  async generateImage(options = {}) {
    const {
      prompt,
      width = 512,
      height = 512,
      style = 'realistic',
      model = this.defaultModel,
      steps = 20,
      seed = null,
      negative_prompt = null
    } = options;

    this.logger.info(`🎨 开始生成图片: ${prompt.substring(0, 50)}...`);

    try {
      // 首先尝试魔搭平台API
      if (this.apiKey) {
        const result = await this.generateWithModelScope({
          prompt,
          width,
          height,
          style,
          model,
          steps,
          seed,
          negative_prompt
        });

        if (result.success) {
          return result;
        }

        this.logger.error('❌ 魔搭平台API失败');
        return {
          success: false,
          error: '魔搭平台API调用失败'
        };
      }

      this.logger.error('❌ 未设置API密钥');
      return {
        success: false,
        error: '未设置MODELSCOPE_API_KEY环境变量'
      };

    } catch (error) {
      this.logger.error('❌ 图片生成失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 使用魔搭平台API生成图片
   */
  async generateWithModelScope(options) {
    const {
      prompt,
      width,
      height,
      style,
      model,
      steps,
      seed,
      negative_prompt
    } = options;

    try {
      // 构建请求数据
      const requestData = {
        model: model,
        input: {
          text: prompt,
          negative_prompt: negative_prompt || this.getDefaultNegativePrompt(style),
          width: width,
          height: height,
          num_inference_steps: steps,
          guidance_scale: 7.5,
          seed: seed || Math.floor(Math.random() * 1000000)
        },
        parameters: {
          style: style,
          quality: 'high'
        }
      };

      this.logger.info(`📡 调用魔搭平台API: ${model}`);

      // 发送请求
      const response = await this.httpClient.post(
        `${this.baseUrl}/text-to-image`,
        requestData
      );

      if (response.status !== 200) {
        throw new Error(`API请求失败: ${response.status} - ${response.statusText}`);
      }

      const result = response.data;

      // 处理不同的响应格式
      if (result.output && result.output.images) {
        // 直接返回图片数据
        const imageData = result.output.images[0];
        return await this.saveImageFromData(imageData, prompt, 'modelscope');
      } else if (result.output && result.output.image_url) {
        // 返回图片URL
        return await this.downloadAndSaveImage(result.output.image_url, prompt, 'modelscope');
      } else if (result.task_id) {
        // 异步任务，需要轮询结果
        return await this.waitForAsyncTask(result.task_id, prompt);
      } else {
        throw new Error(`未知的响应格式: ${JSON.stringify(result)}`);
      }

    } catch (error) {
      this.logger.error('❌ 魔搭平台API调用失败:', error);
      
      if (error.response) {
        this.logger.error(`状态码: ${error.response.status}`);
        this.logger.error(`响应数据: ${JSON.stringify(error.response.data)}`);
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * 等待异步任务完成
   */
  async waitForAsyncTask(taskId, prompt, maxWait = 300) {
    this.logger.info(`⏳ 等待异步任务完成: ${taskId}`);

    const statusUrl = `${this.baseUrl}/tasks/${taskId}`;
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait * 1000) {
      try {
        const response = await this.httpClient.get(statusUrl);
        const result = response.data;

        if (result.status === 'SUCCEEDED') {
          this.logger.info('✅ 异步任务完成');
          
          if (result.output && result.output.images) {
            const imageData = result.output.images[0];
            return await this.saveImageFromData(imageData, prompt, 'modelscope-async');
          } else if (result.output && result.output.image_url) {
            return await this.downloadAndSaveImage(result.output.image_url, prompt, 'modelscope-async');
          }
        } else if (result.status === 'FAILED') {
          throw new Error(`异步任务失败: ${result.message || '未知错误'}`);
        }

        // 等待5秒后重试
        await new Promise(resolve => setTimeout(resolve, 5000));

      } catch (error) {
        this.logger.error('❌ 检查任务状态失败:', error);
        break;
      }
    }

    throw new Error('异步任务超时');
  }





  /**
   * 从数据保存图片
   */
  async saveImageFromData(imageData, prompt, source) {
    try {
      let buffer;

      if (typeof imageData === 'string') {
        // Base64数据
        if (imageData.startsWith('data:image/')) {
          const base64Data = imageData.split(',')[1];
          buffer = Buffer.from(base64Data, 'base64');
        } else {
          buffer = Buffer.from(imageData, 'base64');
        }
      } else {
        buffer = Buffer.from(imageData);
      }

      return await this.saveImageFromBuffer(buffer, prompt, source);

    } catch (error) {
      this.logger.error('❌ 保存图片数据失败:', error);
      throw error;
    }
  }

  /**
   * 从Buffer保存图片
   */
  async saveImageFromBuffer(buffer, prompt, source) {
    try {
      const filename = this.generateFilename(prompt, 'png');
      const filepath = path.join(this.outputDir, filename);

      await fs.writeFile(filepath, buffer);

      this.logger.info(`✅ 图片保存成功: ${filename}`);

      return {
        success: true,
        local_path: filepath,
        filename: filename,
        prompt: prompt,
        source: source,
        size_bytes: buffer.length
      };

    } catch (error) {
      this.logger.error('❌ 保存图片文件失败:', error);
      throw error;
    }
  }

  /**
   * 下载并保存图片
   */
  async downloadAndSaveImage(imageUrl, prompt, source) {
    try {
      this.logger.info(`📥 下载图片: ${imageUrl}`);

      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000
      });

      const buffer = Buffer.from(response.data);
      return await this.saveImageFromBuffer(buffer, prompt, source);

    } catch (error) {
      this.logger.error('❌ 下载图片失败:', error);
      throw error;
    }
  }

  /**
   * 生成文件名
   */
  generateFilename(prompt, extension = 'png') {
    const hash = crypto.createHash('md5').update(prompt).digest('hex').substring(0, 8);
    const timestamp = Date.now();
    const sanitizedPrompt = prompt.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_').substring(0, 20);
    
    return `generated_${sanitizedPrompt}_${hash}_${timestamp}.${extension}`;
  }

  /**
   * 获取默认负面提示词
   */
  getDefaultNegativePrompt(style) {
    const baseNegative = 'low quality, blurry, distorted, watermark, text, signature, username, error, cropped, worst quality, low resolution, jpeg artifacts';

    const styleSpecific = {
      'realistic': ', cartoon, anime, painting, sketch, drawing',
      'cartoon': ', photorealistic, realistic, photography, photo',
      'minimalist': ', cluttered, busy, complex, ornate, detailed',
      'artistic': ', boring, plain, simple, mundane, ordinary'
    };

    return baseNegative + (styleSpecific[style] || '');
  }

  /**
   * 获取生成历史
   */
  async getGenerationHistory(limit = 20) {
    try {
      const files = await fs.readdir(this.outputDir);
      const imageFiles = files.filter(file => 
        /\.(png|jpg|jpeg|webp)$/i.test(file)
      );

      const fileStats = await Promise.all(
        imageFiles.map(async (file) => {
          const filepath = path.join(this.outputDir, file);
          const stats = await fs.stat(filepath);
          return {
            filename: file,
            path: filepath,
            size_bytes: stats.size,
            size_mb: Math.round(stats.size / (1024 * 1024) * 100) / 100,
            created_time: stats.mtime.toISOString(),
            created_timestamp: stats.mtime.getTime()
          };
        })
      );

      // 按创建时间排序
      fileStats.sort((a, b) => b.created_timestamp - a.created_timestamp);

      return {
        success: true,
        total_files: fileStats.length,
        files: fileStats.slice(0, limit),
        output_directory: this.outputDir
      };

    } catch (error) {
      this.logger.error('❌ 获取生成历史失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 清理旧文件
   */
  async cleanupOldFiles(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7天
    try {
      const files = await fs.readdir(this.outputDir);
      const now = Date.now();
      let deletedCount = 0;

      for (const file of files) {
        const filepath = path.join(this.outputDir, file);
        const stats = await fs.stat(filepath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filepath);
          deletedCount++;
        }
      }

      this.logger.info(`🧹 清理了 ${deletedCount} 个旧文件`);
      return { success: true, deleted_count: deletedCount };

    } catch (error) {
      this.logger.error('❌ 清理文件失败:', error);
      return { success: false, error: error.message };
    }
  }
}
