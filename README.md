# MCP 智能图片生成工具 (Node.js版本)

基于魔搭平台的MCP（Model Context Protocol）图片生成工具，专为解决网页开发中的图片生成问题而设计。**全新Node.js版本，支持智能尺寸推断！**

## 🆕 Node.js版本新特性

- 🧠 **智能尺寸推断**：AI自动根据代码上下文、布局需求和图片用途推断最佳尺寸
- 📱 **响应式设计考虑**：自动生成适配不同设备的图片尺寸建议
- 🔍 **代码上下文分析**：解析HTML标签、CSS类名、容器信息等
- 📦 **NPX支持**：可通过 `npx mcp-image-generator` 直接使用
- ⚡ **更快启动**：Node.js原生性能，启动速度更快

## 功能特性

- 🎨 **智能图片生成**：根据网页内容、设计风格和具体需求，自动生成相应的图片
- 🔄 **自动触发机制**：当AI生成包含图片的网页代码时，能够自动调用此MCP工具
- 📏 **智能尺寸适配**：**无需手动指定尺寸**，AI自动推断最佳图片尺寸
- 🔗 **无缝集成**：AI生成代码时能直接引用刚生成的图片路径/URL
- 🆓 **免费使用**：基于魔搭平台的免费大模型API
- 🌐 **多格式支持**：支持PNG、JPG、WebP等常见网页图片格式

## 技术架构

- **MCP协议**：符合Model Context Protocol标准，可被AI助手调用
- **魔搭平台**：使用魔搭平台的免费文生图API (api-inference.modelscope.cn)
- **Node.js实现**：基于Node.js和MCP SDK开发，支持ES模块
- **多种传输**：支持stdio和SSE两种传输协议
- **智能分析**：集成智能尺寸推断、内容分析、提示词优化

## 快速开始

### 环境要求

- Node.js 18.0.0+
- npm 8.0.0+

### 本地安装和运行

```bash
# 克隆项目
git clone https://github.com/your-username/mcp-image-generator.git
cd mcp-image-generator

# 安装依赖
npm install

# 构建项目
npm run build

# 运行MCP服务器
node build/index.js

# 或使用npm script
npm start
```

### 配置API密钥

创建`.env`文件并配置魔搭平台API密钥：

```env
MODELSCOPE_API_KEY=your_api_key_here
OUTPUT_DIR=./generated_images
DEBUG=false
```

### 运行服务器

```bash
# 标准模式（stdio传输）
node build/index.js

# SSE模式
node build/index.js --transport=sse

# 使用MCP Inspector调试
npx @modelcontextprotocol/inspector node build/index.js
```

## 使用方法

### 在Claude Desktop中使用

1. 打开Claude Desktop配置文件
2. 添加MCP服务器配置：

```json
{
  "mcpServers": {
    "image-generator": {
      "command": "node",
      "args": ["/path/to/your/project/build/index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your_api_key"
      }
    }
  }
}
```

### 在Cursor IDE中使用

```json
{
  "mcpServers": {
    "image-generator": {
      "command": "node",
      "args": ["/path/to/your/project/build/index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your_api_key"
      }
    }
  }
}
```

### 在Cline插件中使用

```json
{
  "mcpServers": {
    "image_generator": {
      "command": "node",
      "args": ["/path/to/your/project/build/index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your_api_key"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

### 在其他AI工具中使用

支持任何兼容MCP协议的AI工具。使用 `npx mcp-image-generator` 命令即可。

## 工具功能

### generate_image

根据描述生成图片的核心工具。

**参数：**
- `prompt` (string): 图片描述提示词
- `width` (integer, 可选): 图片宽度，默认512
- `height` (integer, 可选): 图片高度，默认512
- `style` (string, 可选): 图片风格，如"realistic", "cartoon", "artistic"等

**返回：**
- 生成的图片本地路径或URL

### analyze_and_generate

智能分析网页内容并生成合适图片的高级工具。

**参数：**
- `content_type` (string): 内容类型，如"hero-banner", "icon", "background"等
- `description` (string): 内容描述
- `context` (string, 可选): 网页上下文信息

**返回：**
- 分析结果和生成的图片信息

## 开发指南

### 项目结构

```
mcp-image-generator/
├── package.json                    # NPM配置
├── src/                           # 源代码目录
│   ├── index.js                   # 主入口文件
│   ├── server/
│   │   └── mcp-server.js          # MCP服务器核心
│   ├── clients/
│   │   └── modelscope-client.js   # 魔搭平台API客户端
│   ├── analyzers/
│   │   ├── smart-size-inference.js # 🆕 智能尺寸推断算法
│   │   └── web-content-analyzer.js # 网页内容分析器
│   └── utils/
│       ├── prompt-optimizer.js     # 提示词优化器
│       ├── image-cache.js         # 图片缓存管理
│       └── logger.js              # 日志工具
├── build/                         # 构建输出目录（npm run build后生成）
│   └── [src目录的副本]
├── examples/
│   └── mcp_configs.json           # AI工具配置示例
├── .env.example                   # 环境变量示例
├── README.md                      # 项目文档
├── QUICKSTART.md                  # 快速开始指南
├── DEPLOYMENT.md                  # 部署指南
└── PROJECT_SUMMARY.md             # 项目总结
```

### 贡献指南

1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

MIT License

## 工具详细说明

### 核心工具

#### 1. generate_image
基础图片生成工具，根据提示词生成图片。

```python
# 参数说明
{
    "prompt": "图片描述提示词，支持中英文",
    "width": 512,        # 图片宽度，默认512
    "height": 512,       # 图片高度，默认512
    "style": "realistic", # 图片风格
    "model": "wanx-v1"   # 使用的模型
}

# 返回示例
{
    "success": true,
    "message": "图片生成成功",
    "local_path": "./generated_images/generated_abc123.png",
    "filename": "generated_abc123.png",
    "prompt": "modern office building",
    "dimensions": "512x512",
    "style": "realistic",
    "source": "modelscope"
}
```

#### 2. analyze_and_generate
智能分析内容需求并生成合适的图片。

```python
# 参数说明
{
    "content_type": "hero-banner",  # 内容类型
    "description": "科技公司主页横幅",  # 具体描述
    "context": "蓝色主题，现代风格",    # 上下文信息
    "custom_width": 1200,           # 自定义宽度（可选）
    "custom_height": 600            # 自定义高度（可选）
}

# 返回示例
{
    "success": true,
    "analysis": {
        "detected_type": "hero-banner",
        "detected_style": "professional",
        "optimized_prompt": "professional corporate banner, modern design...",
        "dimensions": "1200x600"
    },
    "generation": {
        "local_path": "./generated_images/generated_def456.png",
        "filename": "generated_def456.png"
    }
}
```

#### 3. analyze_webpage_content
分析网页内容并推荐合适的图片类型和风格。

```python
# 参数说明
{
    "page_content": "网页主要内容文本",
    "page_title": "页面标题",
    "meta_description": "页面描述",
    "additional_context": "额外上下文信息"
}

# 返回示例
{
    "success": true,
    "analysis": {
        "website_type": "corporate",
        "industry": "technology",
        "color_scheme": ["blue", "gray"],
        "mood": "professional",
        "target_audience": "技术专业人士和企业决策者",
        "recommended_styles": ["professional", "minimalist"]
    },
    "image_suggestions": [
        {
            "type": "hero-banner",
            "description": "专业的企业主页横幅",
            "prompt": "professional corporate office...",
            "size": "1200x600"
        }
    ]
}
```

#### 4. optimize_prompt
优化图片生成提示词，提高生成质量。

```python
# 参数说明
{
    "prompt": "原始提示词",
    "style": "realistic",
    "enhance_quality": true,
    "color_preference": "warm"  # warm, cool, vibrant, muted
}

# 返回示例
{
    "success": true,
    "optimization": {
        "original": "a cat on chair",
        "optimized": "a cat on chair, realistic, high quality, detailed...",
        "improvements": ["添加了realistic风格增强词", "添加了质量增强词"],
        "quality_score": 0.85
    },
    "suggestions": ["添加更多描述性词汇"],
    "variations": ["cat on chair, artistic style", "cat on chair, minimalist"],
    "negative_prompt": "low quality, blurry, distorted..."
}
```

### 辅助工具

#### 5. batch_generate_images
批量生成多张图片。

#### 6. get_image_suggestions
获取图片提示词建议。

#### 7. list_supported_types
列出支持的图片类型和风格。

#### 8. get_generation_history
获取图片生成历史记录。

## 实际应用场景

### 场景1：网站开发
当AI生成网站代码时，自动调用MCP工具生成合适的图片：

```javascript
// AI生成的网站代码示例
<div class="hero-section">
    <h1>欢迎来到创新科技公司</h1>
    <p>我们提供最先进的AI解决方案</p>
    <!-- MCP工具会自动生成这里的图片 -->
    <img src="./generated_images/hero_banner_tech.png" alt="科技公司横幅">
</div>
```

### 场景2：内容创作
博客文章自动配图：

```markdown
# 人工智能的未来发展

人工智能技术正在快速发展...

<!-- MCP工具根据文章内容自动生成配图 -->
![AI发展趋势](./generated_images/ai_future_trends.png)
```

### 场景3：电商网站
产品图片自动生成：

```html
<div class="product-card">
    <h3>智能手表</h3>
    <!-- 根据产品描述自动生成产品图片 -->
    <img src="./generated_images/smart_watch_product.png" alt="智能手表">
    <p>功能强大的智能穿戴设备</p>
</div>
```

## 高级配置

### 环境变量配置

```bash
# 魔搭平台配置
MODELSCOPE_API_KEY=your_api_key_here

# 图片生成配置
DEFAULT_IMAGE_WIDTH=512
DEFAULT_IMAGE_HEIGHT=512
DEFAULT_IMAGE_FORMAT=PNG
OUTPUT_DIR=./generated_images

# 服务器配置
MCP_SERVER_NAME=image-generator
MCP_SERVER_PORT=8000

# 调试配置
DEBUG=false
LOG_LEVEL=INFO
```

### 自定义风格配置

可以通过修改`image_analyzer.py`中的风格映射来添加自定义风格：

```python
# 添加新的图片风格
self.style_keywords = {
    # 现有风格...
    ImageStyle.CYBERPUNK: [
        "cyberpunk", "neon", "futuristic", "digital", "赛博朋克"
    ],
    ImageStyle.VINTAGE: [
        "vintage", "retro", "classic", "old-fashioned", "复古"
    ]
}
```

## 性能优化

### 1. 缓存机制
工具会自动缓存生成的图片，避免重复生成相同内容。

### 2. 批量处理
使用`batch_generate_images`工具可以提高批量生成效率。

### 3. 异步处理
所有图片生成操作都是异步的，不会阻塞主线程。

### 4. 备用方案
当魔搭平台API不可用时，会自动切换到备用图片生成方案。

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查`.env`文件中的`MODELSCOPE_API_KEY`
   - 确保API密钥有效且有足够的配额

2. **图片生成失败**
   - 检查网络连接
   - 查看错误日志确定具体原因
   - 尝试使用更简单的提示词

3. **MCP服务器连接失败**
   - 确保Python环境正确
   - 检查所有依赖包是否已安装
   - 验证文件路径是否正确

4. **权限错误**
   - 检查输出目录的写入权限
   - 确保用户有足够的文件系统权限

### 调试模式

启用调试模式获取更详细的日志信息：

```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
python image_generator.py
```

## 支持

如有问题或建议，请提交Issue或联系开发者。

### 贡献指南

1. Fork本项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 发起Pull Request

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/your-username/mcp-image-generator.git
cd mcp-image-generator

# 安装开发依赖
uv add --dev pytest pytest-asyncio black isort mypy

# 运行测试
python -m pytest tests/

# 代码格式化
black .
isort .

# 类型检查
mypy .
```
