{"claude_desktop_config": {"description": "Claude Desktop配置示例", "config": {"mcpServers": {"image-generator": {"command": "node", "args": ["/path/to/your/project/build/index.js"], "env": {"MODELSCOPE_API_KEY": "your_api_key_here", "OUTPUT_DIR": "./generated_images", "DEBUG": "false"}}}}}, "cursor_config": {"description": "Cursor IDE配置示例", "config": {"mcpServers": {"image-generator": {"command": "node", "args": ["/path/to/your/project/build/index.js"], "env": {"MODELSCOPE_API_KEY": "your_api_key_here"}}}}}, "cline_config": {"description": "Cline插件配置示例", "config": {"mcpServers": {"image_generator": {"command": "node", "args": ["/path/to/your/project/build/index.js"], "env": {"MODELSCOPE_API_KEY": "your_api_key_here"}, "disabled": false, "autoApprove": []}}}}}