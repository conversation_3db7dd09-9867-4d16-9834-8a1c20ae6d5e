# 部署指南

本指南介绍如何在不同环境中部署MCP智能图片生成工具。

## 本地开发部署

### 基础部署

```bash
# 1. 克隆项目
git clone https://github.com/your-username/mcp-image-generator.git
cd mcp-image-generator

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑.env文件，设置API密钥

# 5. 运行服务器
python image_generator.py
```

### 使用uv部署（推荐）

```bash
# 1. 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 初始化项目
uv venv
source .venv/bin/activate

# 3. 安装依赖
uv add "mcp[cli]" httpx requests pillow python-dotenv

# 4. 运行
uv run image_generator.py
```

## Docker部署

### 创建Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml .
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建输出目录
RUN mkdir -p /app/generated_images

# 设置环境变量
ENV PYTHONPATH=/app
ENV OUTPUT_DIR=/app/generated_images

# 暴露端口（如果使用SSE模式）
EXPOSE 8000

# 运行应用
CMD ["python", "image_generator.py"]
```

### 构建和运行Docker容器

```bash
# 构建镜像
docker build -t mcp-image-generator .

# 运行容器（stdio模式）
docker run -it --rm \
  -v $(pwd)/generated_images:/app/generated_images \
  -e MODELSCOPE_API_KEY=your_api_key \
  mcp-image-generator

# 运行容器（SSE模式）
docker run -d \
  -p 8000:8000 \
  -v $(pwd)/generated_images:/app/generated_images \
  -e MODELSCOPE_API_KEY=your_api_key \
  mcp-image-generator
```

### Docker Compose部署

```yaml
# docker-compose.yml
version: '3.8'

services:
  mcp-image-generator:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./generated_images:/app/generated_images
      - ./logs:/app/logs
    environment:
      - MODELSCOPE_API_KEY=${MODELSCOPE_API_KEY}
      - OUTPUT_DIR=/app/generated_images
      - LOG_LEVEL=INFO
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./generated_images:/var/www/images
    depends_on:
      - mcp-image-generator
    restart: unless-stopped
```

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 云端部署

### 阿里云函数计算部署

1. **准备代码包**
```bash
# 创建部署包
zip -r mcp-image-generator.zip . -x "*.git*" "*.env*" "__pycache__*" "*.pyc"
```

2. **创建函数**
- 登录阿里云函数计算控制台
- 创建新函数，选择Python 3.9运行时
- 上传代码包
- 配置环境变量：`MODELSCOPE_API_KEY`

3. **配置触发器**
```python
# 修改image_generator.py支持函数计算
import os

def handler(event, context):
    """阿里云函数计算入口"""
    # 解析事件参数
    # 调用相应的工具函数
    # 返回结果
    pass

if __name__ == "__main__":
    if os.getenv("FC_RUNTIME_API"):
        # 函数计算环境
        pass
    else:
        # 本地环境
        app.run(transport="stdio")
```

### AWS Lambda部署

1. **创建部署包**
```bash
# 安装依赖到本地目录
pip install -r requirements.txt -t ./package

# 复制代码文件
cp *.py ./package/

# 创建部署包
cd package && zip -r ../lambda-deployment.zip .
```

2. **创建Lambda函数**
```python
# lambda_handler.py
import json
from image_generator import app

def lambda_handler(event, context):
    """AWS Lambda入口函数"""
    try:
        # 解析API Gateway事件
        body = json.loads(event.get('body', '{}'))
        
        # 调用MCP工具
        # 这里需要适配MCP协议到Lambda
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps(result)
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)})
        }
```

### Vercel部署

1. **创建vercel.json**
```json
{
  "functions": {
    "api/generate.py": {
      "runtime": "python3.9"
    }
  },
  "env": {
    "MODELSCOPE_API_KEY": "@modelscope_api_key"
  }
}
```

2. **创建API端点**
```python
# api/generate.py
from http.server import BaseHTTPRequestHandler
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from image_generator import generate_image

class handler(BaseHTTPRequestHandler):
    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))
        
        # 调用图片生成函数
        result = generate_image(**data)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(result).encode())
```

3. **部署到Vercel**
```bash
# 安装Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

## 生产环境配置

### 环境变量管理

```bash
# 生产环境变量
export MODELSCOPE_API_KEY="prod_api_key"
export OUTPUT_DIR="/var/lib/mcp-images"
export LOG_LEVEL="INFO"
export DEBUG="false"
export MAX_CONCURRENT_GENERATIONS="5"
export CACHE_TTL="3600"
```

### 日志配置

```python
# logging_config.py
import logging
import os

def setup_logging():
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=log_format,
        handlers=[
            logging.FileHandler('/var/log/mcp-image-generator.log'),
            logging.StreamHandler()
        ]
    )
```

### 监控和健康检查

```python
# health_check.py
from fastapi import FastAPI
import psutil
import os

app = FastAPI()

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "cpu_usage": psutil.cpu_percent(),
        "memory_usage": psutil.virtual_memory().percent,
        "disk_usage": psutil.disk_usage('/').percent,
        "generated_images_count": len(os.listdir(os.getenv('OUTPUT_DIR', './generated_images')))
    }

@app.get("/metrics")
async def metrics():
    """监控指标端点"""
    return {
        "total_generations": get_total_generations(),
        "success_rate": get_success_rate(),
        "average_generation_time": get_average_generation_time(),
        "api_quota_remaining": get_api_quota()
    }
```

### 负载均衡配置

```nginx
# nginx.conf
upstream mcp_backend {
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}

server {
    listen 80;
    server_name your-domain.com;

    location /mcp/ {
        proxy_pass http://mcp_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_timeout 300s;
    }

    location /images/ {
        alias /var/www/generated_images/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
```

## 安全配置

### API密钥管理

```python
# 使用环境变量或密钥管理服务
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.encryption_key = os.getenv('ENCRYPTION_KEY')
        self.cipher = Fernet(self.encryption_key) if self.encryption_key else None
    
    def get_api_key(self):
        encrypted_key = os.getenv('ENCRYPTED_API_KEY')
        if self.cipher and encrypted_key:
            return self.cipher.decrypt(encrypted_key.encode()).decode()
        return os.getenv('MODELSCOPE_API_KEY')
```

### 访问控制

```python
# 添加API访问控制
from functools import wraps
import jwt

def require_auth(f):
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token or not verify_token(token):
            return {"error": "Unauthorized"}, 401
        return await f(*args, **kwargs)
    return decorated_function

@app.tool()
@require_auth
async def generate_image_secure(*args, **kwargs):
    return await generate_image(*args, **kwargs)
```

## 性能优化

### 缓存策略

```python
# 实现图片缓存
import hashlib
import aiofiles
from pathlib import Path

class ImageCache:
    def __init__(self, cache_dir="./cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def get_cache_key(self, prompt, width, height, style):
        content = f"{prompt}_{width}_{height}_{style}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def get_cached_image(self, cache_key):
        cache_file = self.cache_dir / f"{cache_key}.png"
        if cache_file.exists():
            return str(cache_file)
        return None
    
    async def cache_image(self, cache_key, image_path):
        cache_file = self.cache_dir / f"{cache_key}.png"
        async with aiofiles.open(image_path, 'rb') as src:
            async with aiofiles.open(cache_file, 'wb') as dst:
                await dst.write(await src.read())
```

### 并发控制

```python
# 限制并发生成数量
import asyncio
from asyncio import Semaphore

class ConcurrencyManager:
    def __init__(self, max_concurrent=5):
        self.semaphore = Semaphore(max_concurrent)
    
    async def generate_with_limit(self, *args, **kwargs):
        async with self.semaphore:
            return await generate_image(*args, **kwargs)
```

## 故障排除

### 常见部署问题

1. **依赖安装失败**
   ```bash
   # 使用国内镜像源
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **权限问题**
   ```bash
   # 设置正确的文件权限
   chmod +x image_generator.py
   chown -R app:app /app/generated_images
   ```

3. **内存不足**
   ```bash
   # 增加swap空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

4. **网络连接问题**
   ```bash
   # 测试API连接
   curl -H "Authorization: Bearer your_api_key" \
        https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis
   ```

### 监控和日志

```bash
# 查看应用日志
tail -f /var/log/mcp-image-generator.log

# 监控系统资源
htop
iostat -x 1
df -h

# 检查网络连接
netstat -tulpn | grep :8000
ss -tulpn | grep :8000
```

通过以上部署指南，您可以根据需要选择合适的部署方式，确保MCP智能图片生成工具在各种环境中稳定运行。
