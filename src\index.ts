#!/usr/bin/env node

/**
 * 智慧图片生成 MCP 服务器
 * 基于魔搭平台API，为网页开发提供智能图片生成服务
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import { z } from "zod";
import axios from "axios";
import fs from "fs-extra";
import path from "path";
import crypto from "crypto";

// 配置接口
interface ModelScopeConfig {
  apiKey: string;
  modelId: string;
  baseUrl: string;
  outputDir: string;
  maxImageSize: number;
}

// 图片生成结果接口
interface ImageGenerationResult {
  success: boolean;
  image?: {
    url: string;
    localPath: string;
    width: number;
    height: number;
    format: string;
    type: string;
    prompt: string;
    usageSuggestion: string;
  };
  error?: string;
}

// 尺寸预设
const SIZE_PRESETS = {
  'logo': { width: 200, height: 80 },
  'icon': { width: 64, height: 64 },
  'button-icon': { width: 32, height: 32 },
  'hero-background': { width: 1920, height: 1080 },
  'section-background': { width: 1200, height: 600 },
  'card-image': { width: 400, height: 300 },
  'avatar': { width: 128, height: 128 },
  'thumbnail': { width: 300, height: 200 },
  'banner': { width: 800, height: 200 },
  'square': { width: 512, height: 512 }
};

// 获取配置
function getConfig(): ModelScopeConfig {
  return {
    apiKey: process.env.MODELSCOPE_API_KEY || '',
    modelId: process.env.MODELSCOPE_MODEL_ID || 'stable-diffusion-xl-base-1.0',
    baseUrl: process.env.MODELSCOPE_BASE_URL || 'https://api-inference.modelscope.cn/v1',
    outputDir: process.env.OUTPUT_DIR || './generated_images',
    maxImageSize: parseInt(process.env.MAX_IMAGE_SIZE || '2048')
  };
}

// 确保输出目录存在
async function ensureOutputDir(outputDir: string): Promise<void> {
  await fs.ensureDir(outputDir);
}

// 生成唯一文件名
function generateFileName(type: string, format: string = 'png'): string {
  const timestamp = Date.now();
  const random = crypto.randomBytes(4).toString('hex');
  return `${type}_${timestamp}_${random}.${format}`;
}

// 调用魔搭平台API生成图片
async function callModelScopeAPI(
  prompt: string,
  width: number,
  height: number,
  config: ModelScopeConfig
): Promise<string> {
  try {
    console.error(`🔗 API调用信息:`);
    console.error(`  URL: ${config.baseUrl}`);
    console.error(`  Model: ${config.modelId}`);
    console.error(`  Prompt: ${prompt}`);
    console.error(`  Size: ${width}x${height}`);

    // 魔搭平台API调用格式
    const apiUrl = config.baseUrl.endsWith('/') ?
      `${config.baseUrl}text-to-image-synthesis` :
      `${config.baseUrl}/text-to-image-synthesis`;

    const requestBody = {
      model: config.modelId,
      input: {
        prompt: prompt,
        width: width,
        height: height
      },
      parameters: {
        num_inference_steps: 20,
        guidance_scale: 7.5
      }
    };

    console.error(`📤 请求URL: ${apiUrl}`);
    console.error(`📤 请求体:`, JSON.stringify(requestBody, null, 2));

    const response = await axios.post(apiUrl, requestBody, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });

    console.error(`✅ API响应状态: ${response.status}`);
    console.error(`📄 响应数据:`, JSON.stringify(response.data, null, 2));

    // 处理魔搭平台的响应格式
    if (response.data) {
      // 格式1: output.results数组
      if (response.data.output && response.data.output.results && response.data.output.results[0]) {
        const result = response.data.output.results[0];
        if (result.url) {
          return result.url;
        }
      }

      // 格式2: 直接的images数组
      if (response.data.images && response.data.images[0]) {
        return response.data.images[0].url || response.data.images[0].data;
      }

      // 格式3: 直接的URL字段
      if (response.data.url) {
        return response.data.url;
      }

      // 格式4: base64数据
      if (response.data.data) {
        return response.data.data;
      }
    }

    throw new Error('API响应格式不正确或未找到图片数据');
  } catch (error) {
    console.error('魔搭平台API调用失败:', error);
    throw new Error(`图片生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 下载并保存图片
async function downloadAndSaveImage(
  imageUrl: string, 
  fileName: string, 
  outputDir: string
): Promise<string> {
  try {
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
      timeout: 30000
    });
    
    const filePath = path.join(outputDir, fileName);
    await fs.writeFile(filePath, response.data);
    
    return filePath;
  } catch (error) {
    console.error('图片下载失败:', error);
    throw new Error(`图片下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 创建服务器实例
const server = new Server(
  {
    name: "zhihui-mcp",
    version: "1.0.0",
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 智能分析网页内容并生成提示词
function analyzeContentAndGeneratePrompt(
  content: string,
  imageType: string,
  context?: string
): { prompt: string; suggestedSize: { width: number; height: number } } {
  // 简化的内容分析逻辑
  const lowerContent = content.toLowerCase();
  let style = "modern, clean";
  let subject = "";

  // 分析内容类型和风格
  if (lowerContent.includes("business") || lowerContent.includes("corporate")) {
    style = "professional, corporate, clean";
  } else if (lowerContent.includes("creative") || lowerContent.includes("art")) {
    style = "creative, artistic, colorful";
  } else if (lowerContent.includes("tech") || lowerContent.includes("technology")) {
    style = "modern, tech, futuristic";
  }

  // 根据图片类型生成主题
  switch (imageType) {
    case "logo":
      subject = "logo design";
      break;
    case "icon":
      subject = "icon, symbol";
      break;
    case "background":
      subject = "background, abstract pattern";
      break;
    case "hero":
      subject = "hero image, banner";
      break;
    default:
      subject = "illustration";
  }

  // 构建提示词
  let prompt = `${subject}, ${style}`;
  if (context) {
    prompt += `, ${context}`;
  }
  prompt += ", high quality, professional";

  // 获取建议尺寸
  const suggestedSize = SIZE_PRESETS[imageType as keyof typeof SIZE_PRESETS] || SIZE_PRESETS.square;

  return { prompt, suggestedSize };
}

// 工具定义
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "generate_image",
        description: "基础图片生成功能，根据提示词生成指定尺寸的图片",
        inputSchema: {
          type: "object",
          properties: {
            prompt: {
              type: "string",
              description: "图片生成的提示词描述"
            },
            width: {
              type: "number",
              description: "图片宽度（像素）",
              default: 512
            },
            height: {
              type: "number",
              description: "图片高度（像素）",
              default: 512
            },
            imageType: {
              type: "string",
              description: "图片类型标识",
              enum: ["logo", "icon", "background", "hero", "content", "other"],
              default: "other"
            }
          },
          required: ["prompt"]
        }
      },
      {
        name: "analyze_and_generate",
        description: "智能分析网页内容并自动生成适配的图片（核心功能）",
        inputSchema: {
          type: "object",
          properties: {
            webpageContent: {
              type: "string",
              description: "网页内容或描述"
            },
            imageType: {
              type: "string",
              description: "需要生成的图片类型",
              enum: ["logo", "icon", "background", "hero", "card", "avatar", "banner"],
              default: "content"
            },
            context: {
              type: "string",
              description: "额外的上下文信息或特殊要求"
            },
            customSize: {
              type: "object",
              description: "自定义尺寸（可选）",
              properties: {
                width: { type: "number" },
                height: { type: "number" }
              }
            }
          },
          required: ["webpageContent", "imageType"]
        }
      },
      {
        name: "smart_size_inference",
        description: "根据图片用途智能推断最适合的尺寸",
        inputSchema: {
          type: "object",
          properties: {
            imageType: {
              type: "string",
              description: "图片类型",
              enum: Object.keys(SIZE_PRESETS)
            },
            usageContext: {
              type: "string",
              description: "使用场景描述"
            }
          },
          required: ["imageType"]
        }
      }
    ]
  };
});

// 工具调用处理
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  const config = getConfig();

  // 确保args存在
  if (!args) {
    return {
      content: [
        {
          type: "text",
          text: "错误：缺少必需的参数"
        }
      ]
    };
  }

  // 检查API密钥
  if (!config.apiKey) {
    return {
      content: [
        {
          type: "text",
          text: "错误：未设置MODELSCOPE_API_KEY环境变量。请在MCP配置中设置您的魔搭平台API密钥。"
        }
      ]
    };
  }

  try {
    await ensureOutputDir(config.outputDir);

    switch (name) {
      case "generate_image": {
        const prompt = args.prompt as string;
        const width = (args.width as number) || 512;
        const height = (args.height as number) || 512;
        const imageType = (args.imageType as string) || "other";

        // 验证尺寸限制
        if (width > config.maxImageSize || height > config.maxImageSize) {
          return {
            content: [
              {
                type: "text",
                text: `错误：图片尺寸超出限制。最大尺寸：${config.maxImageSize}x${config.maxImageSize}`
              }
            ]
          };
        }

        console.error(`生成图片: ${prompt} (${width}x${height})`);

        // 调用API生成图片
        const imageUrl = await callModelScopeAPI(prompt, width, height, config);

        // 下载并保存图片
        const fileName = generateFileName(imageType);
        const localPath = await downloadAndSaveImage(imageUrl, fileName, config.outputDir);
        const relativePath = `./${path.relative(process.cwd(), localPath)}`;

        const result: ImageGenerationResult = {
          success: true,
          image: {
            url: relativePath,
            localPath: localPath,
            width: width,
            height: height,
            format: "png",
            type: imageType,
            prompt: prompt,
            usageSuggestion: `适合用作${imageType}，尺寸${width}x${height}`
          }
        };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      }

      case "analyze_and_generate": {
        const webpageContent = args.webpageContent as string;
        const imageType = args.imageType as string;
        const context = args.context as string;
        const customSize = args.customSize as { width?: number; height?: number };

        console.error(`智能分析生成: ${imageType} for content: ${webpageContent.substring(0, 100)}...`);

        // 分析内容并生成提示词
        const analysis = analyzeContentAndGeneratePrompt(webpageContent, imageType, context);

        // 确定最终尺寸
        const finalSize = customSize && customSize.width && customSize.height
          ? { width: customSize.width, height: customSize.height }
          : analysis.suggestedSize;

        // 验证尺寸限制
        if (finalSize.width > config.maxImageSize || finalSize.height > config.maxImageSize) {
          return {
            content: [
              {
                type: "text",
                text: `错误：推断的图片尺寸超出限制。最大尺寸：${config.maxImageSize}x${config.maxImageSize}`
              }
            ]
          };
        }

        // 生成图片
        const imageUrl = await callModelScopeAPI(
          analysis.prompt,
          finalSize.width,
          finalSize.height,
          config
        );

        // 下载并保存图片
        const fileName = generateFileName(imageType);
        const localPath = await downloadAndSaveImage(imageUrl, fileName, config.outputDir);
        const relativePath = `./${path.relative(process.cwd(), localPath)}`;

        const result: ImageGenerationResult = {
          success: true,
          image: {
            url: relativePath,
            localPath: localPath,
            width: finalSize.width,
            height: finalSize.height,
            format: "png",
            type: imageType,
            prompt: analysis.prompt,
            usageSuggestion: `基于内容分析生成的${imageType}，建议用于网页的相应位置`
          }
        };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      }

      case "smart_size_inference": {
        const imageType = args.imageType as string;
        const usageContext = args.usageContext as string;

        console.error(`尺寸推断: ${imageType}`);

        const suggestedSize = SIZE_PRESETS[imageType as keyof typeof SIZE_PRESETS];

        if (!suggestedSize) {
          return {
            content: [
              {
                type: "text",
                text: `错误：不支持的图片类型 "${imageType}"。支持的类型：${Object.keys(SIZE_PRESETS).join(", ")}`
              }
            ]
          };
        }

        const result = {
          imageType: imageType,
          suggestedSize: suggestedSize,
          usageContext: usageContext,
          recommendation: `对于${imageType}类型的图片，建议使用${suggestedSize.width}x${suggestedSize.height}尺寸`
        };

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      }

      default:
        return {
          content: [
            {
              type: "text",
              text: `错误：未知的工具名称 "${name}"`
            }
          ]
        };
    }
  } catch (error) {
    console.error(`工具调用失败 (${name}):`, error);
    return {
      content: [
        {
          type: "text",
          text: `错误：${error instanceof Error ? error.message : '未知错误'}`
        }
      ]
    };
  }
});

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("智慧图片生成 MCP 服务器已启动，等待请求...");
  console.error("支持的工具：generate_image, analyze_and_generate, smart_size_inference");
  console.error("请确保已设置 MODELSCOPE_API_KEY 环境变量");
}

main().catch((error) => {
  console.error("服务器启动失败:", error);
  process.exit(1);
});
