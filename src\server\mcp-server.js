/**
 * MCP智能图片生成服务器
 * 基于Node.js MCP SDK实现
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';

import { ModelScopeClient } from '../clients/modelscope-client.js';
import { SmartSizeInference } from '../analyzers/smart-size-inference.js';
import { WebContentAnalyzer } from '../analyzers/web-content-analyzer.js';
import { PromptOptimizer } from '../utils/prompt-optimizer.js';
import { ImageCache } from '../utils/image-cache.js';
import { Logger } from '../utils/logger.js';

export class MCPImageServer {
  constructor(options = {}) {
    this.options = {
      transport: 'stdio',
      port: 8000,
      debug: false,
      ...options
    };

    this.server = new Server(
      {
        name: 'image-generator',
        version: '1.0.0',
        description: '智能图片生成工具，支持自动尺寸推断和内容分析'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    );

    // 初始化组件
    this.logger = new Logger(this.options.debug);
    this.modelScopeClient = null;
    this.sizeInference = null;
    this.webAnalyzer = null;
    this.promptOptimizer = null;
    this.imageCache = null;

    this.setupHandlers();
  }

  /**
   * 初始化所有组件
   */
  async initializeComponents() {
    try {
      this.logger.info('🔧 初始化组件...');

      this.modelScopeClient = new ModelScopeClient();
      this.sizeInference = new SmartSizeInference();
      this.webAnalyzer = new WebContentAnalyzer();
      this.promptOptimizer = new PromptOptimizer();
      this.imageCache = new ImageCache();

      this.logger.info('✅ 组件初始化完成');
    } catch (error) {
      this.logger.error('❌ 组件初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置MCP处理器
   */
  setupHandlers() {
    // 工具列表处理器
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'generate_image',
            description: '根据提示词生成图片，支持智能尺寸推断',
            inputSchema: {
              type: 'object',
              properties: {
                prompt: {
                  type: 'string',
                  description: '图片描述提示词，支持中英文'
                },
                style: {
                  type: 'string',
                  description: '图片风格',
                  enum: ['realistic', 'cartoon', 'minimalist', 'artistic', 'professional'],
                  default: 'realistic'
                },
                context: {
                  type: 'string',
                  description: '代码上下文，用于智能尺寸推断（可选）'
                },
                width: {
                  type: 'number',
                  description: '图片宽度（可选，不提供时自动推断）'
                },
                height: {
                  type: 'number',
                  description: '图片高度（可选，不提供时自动推断）'
                }
              },
              required: ['prompt']
            }
          },
          {
            name: 'analyze_and_generate',
            description: '智能分析内容需求并生成合适的图片，自动推断最佳尺寸',
            inputSchema: {
              type: 'object',
              properties: {
                content_type: {
                  type: 'string',
                  description: '内容类型',
                  enum: ['hero-banner', 'icon', 'background', 'content-image', 'logo', 'product', 'avatar']
                },
                description: {
                  type: 'string',
                  description: '具体描述'
                },
                context: {
                  type: 'string',
                  description: '上下文信息，包括HTML代码、CSS类名、页面布局等'
                },
                code_context: {
                  type: 'string',
                  description: 'HTML/CSS代码上下文，用于智能尺寸推断'
                }
              },
              required: ['content_type', 'description']
            }
          },
          {
            name: 'smart_size_inference',
            description: '智能推断图片尺寸，基于代码上下文和布局需求',
            inputSchema: {
              type: 'object',
              properties: {
                code_context: {
                  type: 'string',
                  description: 'HTML/CSS代码上下文'
                },
                image_purpose: {
                  type: 'string',
                  description: '图片用途',
                  enum: ['hero', 'logo', 'icon', 'background', 'content', 'product', 'avatar', 'banner']
                },
                layout_info: {
                  type: 'string',
                  description: '布局信息描述'
                },
                responsive_needs: {
                  type: 'boolean',
                  description: '是否需要响应式设计考虑',
                  default: true
                }
              },
              required: ['image_purpose']
            }
          },
          {
            name: 'analyze_webpage_content',
            description: '分析网页内容并推荐合适的图片类型和风格',
            inputSchema: {
              type: 'object',
              properties: {
                page_content: {
                  type: 'string',
                  description: '网页主要内容文本'
                },
                page_title: {
                  type: 'string',
                  description: '页面标题'
                },
                meta_description: {
                  type: 'string',
                  description: '页面描述'
                },
                html_structure: {
                  type: 'string',
                  description: 'HTML结构代码，用于布局分析'
                },
                css_classes: {
                  type: 'string',
                  description: 'CSS类名信息'
                }
              },
              required: ['page_content']
            }
          },
          {
            name: 'optimize_prompt',
            description: '优化图片生成提示词，提高生成质量',
            inputSchema: {
              type: 'object',
              properties: {
                prompt: {
                  type: 'string',
                  description: '原始提示词'
                },
                style: {
                  type: 'string',
                  description: '图片风格',
                  default: 'realistic'
                },
                enhance_quality: {
                  type: 'boolean',
                  description: '是否添加质量增强词',
                  default: true
                },
                color_preference: {
                  type: 'string',
                  description: '颜色偏好',
                  enum: ['warm', 'cool', 'vibrant', 'muted', '']
                }
              },
              required: ['prompt']
            }
          },
          {
            name: 'batch_generate_images',
            description: '批量生成多张图片，支持智能尺寸推断',
            inputSchema: {
              type: 'object',
              properties: {
                prompts: {
                  type: 'string',
                  description: '提示词列表，用换行符分隔'
                },
                style: {
                  type: 'string',
                  description: '图片风格',
                  default: 'realistic'
                },
                auto_size: {
                  type: 'boolean',
                  description: '是否自动推断尺寸',
                  default: true
                },
                context: {
                  type: 'string',
                  description: '代码上下文，用于尺寸推断'
                }
              },
              required: ['prompts']
            }
          },
          {
            name: 'get_generation_history',
            description: '获取图片生成历史记录',
            inputSchema: {
              type: 'object',
              properties: {
                limit: {
                  type: 'number',
                  description: '返回记录数量限制',
                  default: 20
                }
              }
            }
          },
          {
            name: 'list_supported_types',
            description: '列出支持的图片类型和风格',
            inputSchema: {
              type: 'object',
              properties: {}
            }
          },
          {
            name: 'get_image_suggestions',
            description: '获取图片提示词建议',
            inputSchema: {
              type: 'object',
              properties: {
                base_description: {
                  type: 'string',
                  description: '基础描述'
                },
                count: {
                  type: 'number',
                  description: '建议数量',
                  default: 5
                }
              },
              required: ['base_description']
            }
          }
        ]
      };
    });

    // 工具调用处理器
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        this.logger.info(`🔧 调用工具: ${name}`);
        
        switch (name) {
          case 'generate_image':
            return await this.handleGenerateImage(args);
          case 'analyze_and_generate':
            return await this.handleAnalyzeAndGenerate(args);
          case 'smart_size_inference':
            return await this.handleSmartSizeInference(args);
          case 'analyze_webpage_content':
            return await this.handleAnalyzeWebpageContent(args);
          case 'optimize_prompt':
            return await this.handleOptimizePrompt(args);
          case 'batch_generate_images':
            return await this.handleBatchGenerateImages(args);
          case 'get_generation_history':
            return await this.handleGetGenerationHistory(args);
          case 'list_supported_types':
            return await this.handleListSupportedTypes(args);
          case 'get_image_suggestions':
            return await this.handleGetImageSuggestions(args);
          default:
            throw new Error(`未知工具: ${name}`);
        }
      } catch (error) {
        this.logger.error(`❌ 工具调用失败 ${name}:`, error);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                success: false,
                message: `工具调用失败: ${error.message}`,
                tool: name,
                error: error.stack
              }, null, 2)
            }
          ]
        };
      }
    });
  }

  /**
   * 启动服务器
   */
  async start() {
    await this.initializeComponents();

    let transport;
    
    if (this.options.transport === 'sse') {
      transport = new SSEServerTransport('/message', this.options.port);
      this.logger.info(`🌐 SSE服务器启动在端口 ${this.options.port}`);
    } else {
      transport = new StdioServerTransport();
      this.logger.info('📡 Stdio服务器启动');
    }

    await this.server.connect(transport);
    this.logger.info('✅ MCP服务器启动成功');
  }

  /**
   * 停止服务器
   */
  async stop() {
    this.logger.info('🛑 正在停止服务器...');
    await this.server.close();
    this.logger.info('✅ 服务器已停止');
  }

  /**
   * 处理基础图片生成
   */
  async handleGenerateImage(args) {
    const {
      prompt,
      style = 'realistic',
      context = '',
      width = null,
      height = null
    } = args;

    try {
      this.logger.info(`🎨 开始生成图片: ${prompt.substring(0, 50)}...`);

      // 智能尺寸推断（如果未指定尺寸）
      let finalWidth = width;
      let finalHeight = height;
      let sizeInference = null;

      if (!width || !height) {
        sizeInference = await this.sizeInference.inferSize({
          image_purpose: 'content',
          code_context: context,
          responsive_needs: true
        });

        if (sizeInference.success) {
          finalWidth = finalWidth || sizeInference.primary.width;
          finalHeight = finalHeight || sizeInference.primary.height;
        } else {
          finalWidth = finalWidth || 512;
          finalHeight = finalHeight || 512;
        }
      }

      // 生成图片
      const result = await this.modelScopeClient.generateImage({
        prompt,
        width: finalWidth,
        height: finalHeight,
        style
      });

      const response = {
        success: result.success,
        message: result.success ? '图片生成成功' : `图片生成失败: ${result.error}`,
        generation: result.success ? {
          local_path: result.local_path,
          filename: result.filename,
          prompt: prompt,
          dimensions: `${finalWidth}x${finalHeight}`,
          style: style,
          source: result.source
        } : null,
        size_inference: sizeInference ? {
          used_smart_sizing: !width || !height,
          inferred_size: sizeInference.primary,
          confidence: sizeInference.analysis?.confidence,
          alternatives: sizeInference.alternatives
        } : null,
        error: result.success ? null : result.error
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 图片生成处理失败:', error);
      throw error;
    }
  }

  /**
   * 处理智能分析生成
   */
  async handleAnalyzeAndGenerate(args) {
    const {
      content_type,
      description,
      context = '',
      code_context = ''
    } = args;

    try {
      this.logger.info(`🧠 开始智能分析生成: ${content_type} - ${description}`);

      // 1. 智能尺寸推断
      const sizeInference = await this.sizeInference.inferSize({
        image_purpose: content_type,
        code_context: code_context,
        layout_info: context,
        responsive_needs: true
      });

      // 2. 提示词优化
      const promptOptimization = await this.promptOptimizer.optimizePrompt({
        prompt: description,
        style: 'realistic',
        enhance_quality: true
      });

      // 3. 生成图片
      const result = await this.modelScopeClient.generateImage({
        prompt: promptOptimization.optimized,
        width: sizeInference.success ? sizeInference.primary.width : 512,
        height: sizeInference.success ? sizeInference.primary.height : 512,
        style: 'realistic'
      });

      const response = {
        success: result.success,
        message: result.success ? '智能分析和图片生成成功' : '生成失败',
        analysis: {
          content_type: content_type,
          size_inference: sizeInference.success ? {
            detected_size: sizeInference.primary,
            confidence: sizeInference.analysis?.confidence,
            analysis_hints: sizeInference.analysis
          } : null,
          prompt_optimization: {
            original: description,
            optimized: promptOptimization.optimized,
            improvements: promptOptimization.improvements,
            quality_score: promptOptimization.quality_score
          }
        },
        generation: result.success ? {
          local_path: result.local_path,
          filename: result.filename,
          source: result.source
        } : null,
        input: {
          content_type,
          description,
          context,
          code_context
        },
        error: result.success ? null : result.error
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 智能分析生成失败:', error);
      throw error;
    }
  }

  /**
   * 处理智能尺寸推断
   */
  async handleSmartSizeInference(args) {
    const {
      code_context = '',
      image_purpose,
      layout_info = '',
      responsive_needs = true
    } = args;

    try {
      this.logger.info(`📏 开始智能尺寸推断: ${image_purpose}`);

      const result = await this.sizeInference.inferSize({
        code_context,
        image_purpose,
        layout_info,
        responsive_needs
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 智能尺寸推断失败:', error);
      throw error;
    }
  }

  /**
   * 处理网页内容分析
   */
  async handleAnalyzeWebpageContent(args) {
    const {
      page_content,
      page_title = '',
      meta_description = '',
      html_structure = '',
      css_classes = ''
    } = args;

    try {
      this.logger.info('🔍 开始分析网页内容...');

      const result = await this.webAnalyzer.analyzeContent({
        page_content,
        page_title,
        meta_description,
        html_structure,
        css_classes
      });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 网页内容分析失败:', error);
      throw error;
    }
  }

  /**
   * 处理提示词优化
   */
  async handleOptimizePrompt(args) {
    const {
      prompt,
      style = 'realistic',
      enhance_quality = true,
      color_preference = ''
    } = args;

    try {
      this.logger.info(`🔧 开始优化提示词: ${prompt.substring(0, 50)}...`);

      const optimization = await this.promptOptimizer.optimizePrompt({
        prompt,
        style,
        enhance_quality,
        color_preference: color_preference || null
      });

      const suggestions = this.promptOptimizer.suggestImprovements(prompt);
      const variations = this.promptOptimizer.createVariations(prompt, 3);

      const response = {
        success: true,
        message: '提示词优化完成',
        optimization: optimization,
        suggestions: suggestions,
        variations: variations,
        negative_prompt: this.promptOptimizer.generateNegativePrompt(style)
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 提示词优化失败:', error);
      throw error;
    }
  }

  /**
   * 处理批量图片生成
   */
  async handleBatchGenerateImages(args) {
    const {
      prompts,
      style = 'realistic',
      auto_size = true,
      context = ''
    } = args;

    try {
      const promptList = prompts.split('\n').map(p => p.trim()).filter(p => p);

      if (promptList.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                success: false,
                message: '没有有效的提示词'
              }, null, 2)
            }
          ]
        };
      }

      this.logger.info(`🎨 开始批量生成 ${promptList.length} 张图片`);

      const results = [];
      for (let i = 0; i < promptList.length; i++) {
        const prompt = promptList[i];

        try {
          this.logger.info(`📸 生成第 ${i + 1}/${promptList.length} 张: ${prompt.substring(0, 30)}...`);

          let width = 512;
          let height = 512;

          // 智能尺寸推断
          if (auto_size) {
            const sizeInference = await this.sizeInference.inferSize({
              image_purpose: 'content',
              code_context: context,
              responsive_needs: true
            });

            if (sizeInference.success) {
              width = sizeInference.primary.width;
              height = sizeInference.primary.height;
            }
          }

          const result = await this.modelScopeClient.generateImage({
            prompt,
            width,
            height,
            style
          });

          results.push({
            index: i + 1,
            prompt: prompt,
            success: result.success,
            local_path: result.success ? result.local_path : null,
            filename: result.success ? result.filename : null,
            dimensions: `${width}x${height}`,
            error: result.success ? null : result.error
          });

          // 添加延迟避免API限制
          if (i < promptList.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

        } catch (error) {
          results.push({
            index: i + 1,
            prompt: prompt,
            success: false,
            error: error.message
          });
        }
      }

      const successfulCount = results.filter(r => r.success).length;

      const response = {
        success: true,
        message: `批量生成完成，成功 ${successfulCount}/${promptList.length} 张`,
        total_count: promptList.length,
        successful_count: successfulCount,
        results: results
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 批量生成失败:', error);
      throw error;
    }
  }

  /**
   * 处理获取生成历史
   */
  async handleGetGenerationHistory(args) {
    const { limit = 20 } = args;

    try {
      this.logger.info('📚 获取图片生成历史...');

      const result = await this.modelScopeClient.getGenerationHistory(limit);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 获取生成历史失败:', error);
      throw error;
    }
  }

  /**
   * 处理列出支持的类型
   */
  async handleListSupportedTypes(args) {
    try {
      const response = {
        success: true,
        supported_types: [
          {
            value: 'hero-banner',
            description: '网站主页横幅图片',
            typical_size: '1200x600',
            use_cases: ['网站首页', '着陆页', '产品展示']
          },
          {
            value: 'icon',
            description: '图标或小型标识',
            typical_size: '64x64',
            use_cases: ['功能图标', '导航图标', '按钮图标']
          },
          {
            value: 'background',
            description: '背景图片或纹理',
            typical_size: '1920x1080',
            use_cases: ['页面背景', '区域背景', '纹理图案']
          },
          {
            value: 'content-image',
            description: '内容配图',
            typical_size: '800x600',
            use_cases: ['文章配图', '博客图片', '新闻图片']
          },
          {
            value: 'logo',
            description: '标志或品牌图标',
            typical_size: '200x200',
            use_cases: ['公司标志', '品牌标识', '产品标志']
          },
          {
            value: 'product',
            description: '产品展示图片',
            typical_size: '600x600',
            use_cases: ['电商产品', '产品展示', '商品图片']
          },
          {
            value: 'avatar',
            description: '头像或个人图片',
            typical_size: '128x128',
            use_cases: ['用户头像', '个人资料', '团队成员']
          }
        ],
        supported_styles: [
          {
            value: 'realistic',
            description: '真实照片风格',
            best_for: ['产品图片', '人物照片', '风景图片']
          },
          {
            value: 'cartoon',
            description: '卡通动漫风格',
            best_for: ['儿童内容', '游戏图标', '趣味插图']
          },
          {
            value: 'minimalist',
            description: '简约现代风格',
            best_for: ['企业网站', '简洁设计', '现代界面']
          },
          {
            value: 'artistic',
            description: '艺术创意风格',
            best_for: ['创意设计', '艺术作品', '独特视觉']
          },
          {
            value: 'professional',
            description: '专业商务风格',
            best_for: ['商务网站', '企业形象', '正式场合']
          }
        ],
        smart_sizing: {
          enabled: true,
          description: '智能尺寸推断功能，可根据代码上下文自动推断最佳图片尺寸',
          features: [
            '代码上下文分析',
            '布局需求识别',
            '响应式设计考虑',
            '多种尺寸建议'
          ]
        }
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 列出支持类型失败:', error);
      throw error;
    }
  }

  /**
   * 处理获取图片建议
   */
  async handleGetImageSuggestions(args) {
    const { base_description, count = 5 } = args;

    try {
      this.logger.info(`💡 生成图片建议: ${base_description}`);

      const variations = this.promptOptimizer.createVariations(base_description, count);

      const response = {
        success: true,
        base_description: base_description,
        suggestions: variations.map((variation, index) => ({
          index: index + 1,
          prompt: variation,
          description: `变体 ${index + 1}：${variation.substring(0, 50)}...`
        })),
        count: variations.length,
        tips: [
          '使用具体而详细的描述可以获得更好的效果',
          '考虑添加风格、颜色和情绪描述',
          '避免使用负面词汇，专注于正面描述'
        ]
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(response, null, 2)
          }
        ]
      };

    } catch (error) {
      this.logger.error('❌ 获取图片建议失败:', error);
      throw error;
    }
  }
}
