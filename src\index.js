/**
 * MCP智能图片生成服务器主入口
 * Node.js版本 - 支持智能尺寸推断和内容分析
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';
import { MCPImageServer } from './server/mcp-server.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

// 加载环境变量
dotenv.config({ path: join(projectRoot, '.env') });

// 解析命令行参数
const args = process.argv.slice(2);
const options = {
  transport: 'stdio',
  port: 8000,
  debug: false,
  config: null
};

// 解析参数
args.forEach(arg => {
  if (arg.startsWith('--transport=')) {
    options.transport = arg.split('=')[1];
  } else if (arg.startsWith('--port=')) {
    options.port = parseInt(arg.split('=')[1]);
  } else if (arg === '--debug') {
    options.debug = true;
  } else if (arg.startsWith('--config=')) {
    options.config = arg.split('=')[1];
  }
});

// 设置调试模式
if (options.debug) {
  process.env.DEBUG = 'true';
  process.env.LOG_LEVEL = 'DEBUG';
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🎨 MCP智能图片生成工具 (Node.js版本)');
    console.log('📝 基于魔搭平台API，支持智能尺寸推断和内容分析');
    console.log('🔧 使用 Ctrl+C 停止服务器');
    console.log('=' * 50);

    // 创建并启动服务器
    const server = new MCPImageServer(options);
    await server.start();

    // 处理退出信号
    process.on('SIGINT', async () => {
      console.log('\n👋 正在关闭服务器...');
      await server.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await server.stop();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error.message);
    if (options.debug) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  if (process.env.DEBUG) {
    console.error('Promise:', promise);
  }
  process.exit(1);
});

// 启动应用
main();
